package auth

import (
	"strconv"

	"github.com/gin-gonic/gin"
)

// ContextKey type for context keys
type ContextKey string

// DefaultAuthMiddlewareKey is the key used to store auth data in context
const DefaultAuthMiddlewareKey = "auth"

// Các key dùng trong context - thống nhất toàn bộ hệ thống
const (
	UserIDKey = "userID" // Key lưu user ID trong context
	ClaimsKey = "claims" // Key lưu JWT claims trong context
	TenantKey = "tenant" // Key lưu thông tin tenant trong context
)

// GetUserID lấy user ID từ context
func GetUserID(c *gin.Context) *uint {
	userID, exists := c.Get(UserIDKey)
	if !exists {
		return nil
	}

	// Tạo biến tạm để lưu kết quả của type assertion
	value := userID.(uint)
	return &value
}

// GetTenantID lấy tenant ID từ context
// Tenant ID được lưu trong context bởi TenantMiddleware
// TenantMiddleware lấy tenant ID từ header X-Tenant-ID
func GetTenantID(c *gin.Context) uint {
	tenantID, exists := c.Get(TenantKey)
	if !exists {
		return 0
	}

	return tenantID.(uint)
}

// GetWebsiteID lấy website ID từ context hoặc header X-Website-ID
// Nếu không có, fallback về tenantID (temporary solution)
func GetWebsiteID(c *gin.Context) uint {
	// 1. Kiểm tra xem có websiteID trong context không (đã được set bởi middleware)
	websiteID, exists := c.Get("websiteID")
	if exists {
		return websiteID.(uint)
	}

	// 2. Thử lấy từ header X-Website-ID
	websiteIDStr := c.GetHeader("X-Website-ID")
	if websiteIDStr != "" {
		if id, err := parseUintFromString(websiteIDStr); err == nil {
			// Lưu vào context để tránh parse lại
			c.Set("websiteID", id)
			return id
		}
	}

	// 3. Fallback: sử dụng tenantID làm websiteID (temporary solution)
	return GetTenantID(c)
}

// parseUintFromString chuyển đổi string thành uint
func parseUintFromString(s string) (uint, error) {
	// Import strconv sẽ được thêm tự động bởi IDE
	if val, err := strconv.ParseUint(s, 10, 32); err == nil {
		return uint(val), nil
	} else {
		return 0, err
	}
}
