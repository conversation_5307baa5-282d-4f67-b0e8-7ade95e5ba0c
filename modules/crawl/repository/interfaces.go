package repository

import (
	"context"
	"time"

	"blog-api-v1/modules/crawl/dto/request"
	"blog-api-v1/modules/crawl/dto/response"
	"blog-api-v1/modules/crawl/internal"
	"blog-api-v1/modules/crawl/models"
)

// CrawlJobRepository defines the interface for crawl job repository
type CrawlJobRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, tenantID, userID uint, job *models.CrawlJob) error
	GetByID(ctx context.Context, tenantID, userID, id uint) (*models.CrawlJob, error)
	Update(ctx context.Context, tenantID, userID uint, job *models.CrawlJob) error
	Delete(ctx context.Context, tenantID, userID, id uint) error

	// List and search operations
	List(ctx context.Context, tenantID, userID uint, req request.ListCrawlJobRequest) ([]*models.CrawlJob, error)
	GetByStatus(ctx context.Context, tenantID uint, status string) ([]*models.CrawlJob, error)
	GetActiveJobs(ctx context.Context, tenantID, userID uint) ([]*models.CrawlJob, error)
	GetScheduledJobs(ctx context.Context) ([]*models.CrawlJob, error)

	// Status operations
	UpdateStatus(ctx context.Context, tenantID, userID, id uint, status string) error
	UpdateProgress(ctx context.Context, tenantID, userID, id uint, progress *internal.CrawlProgress) error
	IncrementRetryCount(ctx context.Context, tenantID, id uint) error

	// Statistics
	GetStats(ctx context.Context, tenantID, userID uint) (*response.CrawlJobStatsResponse, error)
	CountByStatus(ctx context.Context, tenantID uint, status string) (int64, error)

	// Bulk operations
	BulkUpdate(ctx context.Context, tenantID, userID uint, req request.BulkUpdateCrawlJobRequest) error
	UpdateLastRunAt(ctx context.Context, tenantID, userID, id uint, lastRunAt time.Time) error
	UpdateNextRunAt(ctx context.Context, tenantID, userID, id uint, nextRunAt time.Time) error
}

// CrawlArticleRepository defines the interface for crawl article repository
type CrawlArticleRepository interface {
	// Basic CRUD operations
	Create(ctx context.Context, tenantID, userID uint, article *models.CrawlArticle) error
	BatchCreate(ctx context.Context, tenantID, userID uint, articles []*models.CrawlArticle) error
	GetByID(ctx context.Context, tenantID, userID, id uint) (*models.CrawlArticle, error)
	Update(ctx context.Context, tenantID, userID uint, article *models.CrawlArticle) error
	Delete(ctx context.Context, tenantID, userID, id uint) error

	// List and search operations
	List(ctx context.Context, tenantID, userID uint, req request.ListCrawlArticleRequest) ([]*models.CrawlArticle, error)
	GetByJobID(ctx context.Context, tenantID, jobID uint, req request.ListCrawlArticleRequest) (*response.CrawlArticleListResponse, error)
	GetByURL(ctx context.Context, tenantID uint, url string) (*models.CrawlArticle, error)
	GetByHash(ctx context.Context, tenantID uint, hash string) (*models.CrawlArticle, error)
	GetByStatus(ctx context.Context, tenantID uint, status string) ([]*models.CrawlArticle, error)

	// Search operations
	Search(ctx context.Context, tenantID, userID uint, req request.SearchCrawlArticleRequest) ([]*models.CrawlArticle, error)
	GetSimilarArticles(ctx context.Context, tenantID uint, title string, limit int) ([]*models.CrawlArticle, error)

	// Status operations
	UpdateStatus(ctx context.Context, tenantID, userID, id uint, status string) error
	MarkAsProcessed(ctx context.Context, tenantID, id uint) error
	MarkAsFailed(ctx context.Context, tenantID, id uint, errorMsg string) error
	MarkAsDuplicate(ctx context.Context, tenantID, id uint) error
	IncrementRetryCount(ctx context.Context, tenantID, id uint) error

	// Batch operations
	BatchUpdateStatus(ctx context.Context, tenantID uint, ids []uint, status string) error
	BatchDelete(ctx context.Context, tenantID uint, ids []uint) error

	// Statistics
	GetStats(ctx context.Context, tenantID, userID uint) (*response.CrawlArticleStatsResponse, error)
	CountByStatus(ctx context.Context, tenantID uint, status string) (int64, error)
	CountByJobID(ctx context.Context, tenantID, jobID uint) (int64, error)

	// Cleanup operations
	DeleteOldArticles(ctx context.Context, tenantID uint, days int) (int64, error)
	DeleteByJobID(ctx context.Context, tenantID, jobID uint) error

	// Bulk operations
	BulkUpdate(ctx context.Context, tenantID, userID uint, req request.BulkUpdateCrawlArticleRequest) error
	GetByHashContent(ctx context.Context, tenantID, userID uint, hashContent string) (*models.CrawlArticle, error)
	GetByCrawlJobID(ctx context.Context, tenantID, userID, crawlJobID uint) ([]*models.CrawlArticle, error)
	CleanupOldArticles(ctx context.Context, tenantID, userID uint, days int) (int64, error)
	CleanupFailedArticles(ctx context.Context, tenantID, userID uint) (int64, error)
}