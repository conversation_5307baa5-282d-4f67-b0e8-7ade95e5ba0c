package crawl

import (
	"go.uber.org/fx"

	"blog-api-v1/internal/fx/modules"
	"blog-api-v1/modules/crawl/repository"
	"blog-api-v1/modules/crawl/repository/mysql"
	"blog-api-v1/modules/crawl/services"
)

func init() {
	modules.GlobalRegistry.Register(&CrawlModule{})
}

// CrawlModule implements the FX module interface
type CrawlModule struct{}

// Name returns the module name
func (m *CrawlModule) Name() string {
	return "crawl"
}

// Dependencies returns module dependencies
func (m *CrawlModule) Dependencies() []string {
	return []string{"tenant", "auth", "rbac"}
}

// Priority returns module loading priority
func (m *CrawlModule) Priority() int {
	return 60 // Load after blog module
}

// Enabled returns whether the module is enabled
func (m *CrawlModule) Enabled(config map[string]interface{}) bool {
	if enabled, ok := config["enabled"].(bool); ok {
		return enabled
	}
	return true // Default to enabled
}

// GetMigrationPath returns path to module migrations
func (m *CrawlModule) GetMigrationPath() string {
	return "modules/crawl/migrations"
}

// GetMigrationOrder returns migration priority order
func (m *CrawlModule) GetMigrationOrder() int {
	return 60 // Crawl module runs after blog module
}

// Module returns FX options for the module
func (m *CrawlModule) Module() fx.Option {
	return fx.Module("crawl",
		// Providers
		fx.Provide(
			// Configuration
			NewCrawlConfig,

			// Repositories
			fx.Annotate(mysql.NewCrawlJobRepository, fx.As(new(repository.CrawlJobRepository))),
			fx.Annotate(mysql.NewCrawlArticleRepository, fx.As(new(repository.CrawlArticleRepository))),

			// Services
			NewCrawlJobService,
			NewCrawlArticleService,
			NewCrawlService,

			// API Handler
			NewCrawlHandler,
		),

		// Route registration
		fx.Invoke(RegisterCrawlRoutes),
	)
}