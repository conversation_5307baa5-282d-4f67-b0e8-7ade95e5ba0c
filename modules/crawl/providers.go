package crawl

import (
	"github.com/gin-gonic/gin"

	"wnapi/internal/middleware"
	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/config"
	"wnapi/internal/pkg/logger"
	"wnapi/internal/pkg/permission"
	"wnapi/internal/pkg/queue"
	"wnapi/modules/crawl/api"
	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/repository"
	"wnapi/modules/crawl/repository/mysql"
	"wnapi/modules/crawl/service"
)

// NewCrawlConfig creates crawl configuration
func NewCrawlConfig(cfg config.Config) (*internal.CrawlConfig, error) {
	return internal.NewCrawlConfigFromAppConfig(cfg)
}

// NewCrawlJobService creates crawl job service
func NewCrawlJobService(
	crawlJobRepo repository.CrawlJobRepository,
	queueClient queue.QueueClient,
	logger logger.Logger,
) service.CrawlJobService {
	// Convert interface to concrete type
	crawlJobRepoImpl := crawlJobRepo.(*mysql.CrawlJobRepository)
	return service.NewCrawlJobService(crawlJobRepoImpl, queueClient, logger)
}

// NewCrawlArticleService creates crawl article service
func NewCrawlArticleService(
	crawlArticleRepo repository.CrawlArticleRepository,
	logger logger.Logger,
) service.CrawlArticleService {
	// Convert interface to concrete type
	crawlArticleRepoImpl := crawlArticleRepo.(*mysql.CrawlArticleRepository)
	return service.NewCrawlArticleService(crawlArticleRepoImpl, logger)
}

// NewCrawlService creates crawl service with Colly
func NewCrawlService(
	crawlJobService service.CrawlJobService,
	crawlArticleService service.CrawlArticleService,
	queueClient queue.QueueClient,
	logger logger.Logger,
) service.CrawlService {
	return service.NewCrawlService(crawlJobService, crawlArticleService, queueClient, logger)
}

// NewCrawlHandler creates crawl API handler
func NewCrawlHandler(
	crawlJobService service.CrawlJobService,
	crawlArticleService service.CrawlArticleService,
	crawlService service.CrawlService,
	middlewareFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	log logger.Logger,
	tenantService middleware.TenantService,
) *api.Handler {
	return api.NewHandler(crawlJobService, crawlArticleService, crawlService, middlewareFactory, jwtService, log, tenantService)
}

// RegisterCrawlRoutes registers crawl routes with gin.Engine
func RegisterCrawlRoutes(handler *api.Handler, engine *gin.Engine, log logger.Logger) error {
	log.Info("Registering crawl routes")

	// Register all crawl routes using the handler's RegisterRoutesWithEngine method
	if err := handler.RegisterRoutesWithEngine(engine); err != nil {
		log.Error("Failed to register crawl routes", "error", err)
		return err
	}

	log.Info("Crawl routes registered successfully")
	return nil
}