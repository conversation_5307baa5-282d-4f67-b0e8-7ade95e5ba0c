package crawl

import (
	"github.com/gin-gonic/gin"

	"blog-api-v1/modules/crawl/handlers"
	"blog-api-v1/modules/crawl/internal"
	"blog-api-v1/modules/crawl/repository"
	"blog-api-v1/modules/crawl/repository/mysql"
	"blog-api-v1/modules/crawl/services"
	"blog-api-v1/pkg/config"
	"blog-api-v1/pkg/queue"
)

// NewCrawlConfig creates crawl configuration
func NewCrawlConfig(cfg *config.Config) (*internal.CrawlConfig, error) {
	return internal.NewCrawlConfigFromAppConfig(cfg)
}

// NewCrawlJobService creates crawl job service
func NewCrawlJobService(
	crawlJobRepo repository.CrawlJobRepository,
	queueClient queue.Queue,
) *services.CrawlJobService {
	return services.NewCrawlJobService(crawlJobRepo, queueClient)
}

// NewCrawlArticleService creates crawl article service
func NewCrawlArticleService(
	crawlArticleRepo repository.CrawlArticleRepository,
) *services.CrawlArticleService {
	return services.NewCrawlArticleService(crawlArticleRepo)
}

// NewCrawlService creates crawl service with Colly
func NewCrawlService(
	crawlJobService *services.CrawlJobService,
	crawlArticleService *services.CrawlArticleService,
	queueClient queue.Queue,
) *services.CrawlService {
	return services.NewCrawlService(crawlJobService, crawlArticleService, queueClient)
}

// NewCrawlHandler creates crawl API handler
func NewCrawlHandler(
	crawlJobService *services.CrawlJobService,
	crawlArticleService *services.CrawlArticleService,
	crawlService *services.CrawlService,
) *handlers.CrawlHandler {
	return handlers.NewCrawlHandler(crawlJobService, crawlArticleService, crawlService)
}