package crawl

import (
	"github.com/gin-gonic/gin"

	"blog-api-v1/modules/crawl/handlers"
	"blog-api-v1/modules/crawl/internal"
	"blog-api-v1/modules/crawl/repository"
	"blog-api-v1/modules/crawl/repository/mysql"
	"blog-api-v1/modules/crawl/services"
	"blog-api-v1/pkg/config"
	"blog-api-v1/pkg/queue"
)

// NewCrawlConfig creates crawl configuration
func NewCrawlConfig(cfg *config.Config) (*internal.CrawlConfig, error) {
	return internal.NewCrawlConfigFromAppConfig(cfg)
}

// NewCrawlJobService creates crawl job service
func NewCrawlJobService(
	crawlJobRepo repository.CrawlJobRepository,
	queueClient queue.Queue,
) *services.CrawlJobService {
	return services.NewCrawlJobService(crawlJobRepo, queueClient)
}

// NewCrawlArticleService creates crawl article service
func NewCrawlArticleService(
	crawlArticleRepo repository.CrawlArticleRepository,
) *services.CrawlArticleService {
	return services.NewCrawlArticleService(crawlArticleRepo)
}

// NewCrawlService creates crawl service with Colly
func NewCrawlService(
	crawlJobService *services.CrawlJobService,
	crawlArticleService *services.CrawlArticleService,
	queueClient queue.Queue,
) *services.CrawlService {
	return services.NewCrawlService(crawlJobService, crawlArticleService, queueClient)
}

// NewCrawlHandler creates crawl API handler
func NewCrawlHandler(
	crawlJobService service.CrawlJobService,
	crawlArticleService service.CrawlArticleService,
	crawlService service.CrawlService,
	middlewareFactory *permission.MiddlewareFactory,
	jwtService *auth.JWTService,
	log logger.Logger,
	tenantService middleware.TenantService,
) *api.Handler {
	return api.NewHandler(crawlJobService, crawlArticleService, crawlService, middlewareFactory, jwtService, log, tenantService)
}

// RegisterCrawlRoutes registers crawl routes with gin.Engine
func RegisterCrawlRoutes(handler *api.Handler, engine *gin.Engine, log logger.Logger) error {
	log.Info("Registering crawl routes")

	// Register all crawl routes using the handler's RegisterRoutesWithEngine method
	if err := handler.RegisterRoutesWithEngine(engine); err != nil {
		log.Error("Failed to register crawl routes", "error", err)
		return err
	}

	log.Info("Crawl routes registered successfully")
	return nil
}