package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"go.uber.org/zap"

	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/repository"
	"wnapi/modules/crawl/services"
	"wnapi/pkg/queue"
)

// Crawl<PERSON>ob represents a crawl job in the queue
type CrawlJob struct {
	JobID     uint      `json:"job_id"`
	TenantID  uint      `json:"tenant_id"`
	Priority  int       `json:"priority"`
	RetryCount int      `json:"retry_count"`
	CreatedAt time.Time `json:"created_at"`
}

// CrawlWorker handles crawl job processing
type CrawlWorker struct {
	queue            queue.Queue
	crawlJobRepo     repository.CrawlJobRepository
	crawlService     *services.CrawlService
	logger           *zap.Logger
	config           *internal.CrawlConfig
	workers          int
	stopChan         chan struct{}
	wg               sync.WaitGroup
	running          bool
	mu               sync.RWMutex
}

// NewCrawlWorker creates a new crawl worker
func NewCrawlWorker(
	queue queue.Queue,
	crawlJobRepo repository.CrawlJobRepository,
	crawlService *services.CrawlService,
	config *internal.CrawlConfig,
	logger *zap.Logger,
) *CrawlWorker {
	return &CrawlWorker{
		queue:        queue,
		crawlJobRepo: crawlJobRepo,
		crawlService: crawlService,
		logger:       logger,
		config:       config,
		workers:      config.QueueWorkerCount,
		stopChan:     make(chan struct{}),
	}
}

// Start starts the crawl workers
func (w *CrawlWorker) Start() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.running {
		return fmt.Errorf("crawl worker is already running")
	}

	w.logger.Info("Starting crawl workers", zap.Int("worker_count", w.workers))

	// Start worker goroutines
	for i := 0; i < w.workers; i++ {
		w.wg.Add(1)
		go w.worker(i)
	}

	w.running = true
	w.logger.Info("Crawl workers started successfully")
	return nil
}

// Stop stops the crawl workers
func (w *CrawlWorker) Stop() {
	w.mu.Lock()
	defer w.mu.Unlock()

	if !w.running {
		return
	}

	w.logger.Info("Stopping crawl workers")

	// Signal workers to stop
	close(w.stopChan)

	// Wait for all workers to finish
	w.wg.Wait()

	w.running = false
	w.logger.Info("Crawl workers stopped")
}

// IsRunning returns whether the worker is running
func (w *CrawlWorker) IsRunning() bool {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.running
}

// EnqueueJob adds a crawl job to the queue
func (w *CrawlWorker) EnqueueJob(ctx context.Context, jobID, tenantID uint, priority int) error {
	w.logger.Info("Enqueuing crawl job", 
		zap.Uint("job_id", jobID),
		zap.Uint("tenant_id", tenantID),
		zap.Int("priority", priority))

	crawlJob := CrawlJob{
		JobID:     jobID,
		TenantID:  tenantID,
		Priority:  priority,
		RetryCount: 0,
		CreatedAt: time.Now(),
	}

	data, err := json.Marshal(crawlJob)
	if err != nil {
		return fmt.Errorf("failed to marshal crawl job: %w", err)
	}

	err = w.queue.Enqueue(ctx, w.config.QueueName, data, priority)
	if err != nil {
		return fmt.Errorf("failed to enqueue crawl job: %w", err)
	}

	w.logger.Info("Crawl job enqueued successfully", zap.Uint("job_id", jobID))
	return nil
}

// worker processes jobs from the queue
func (w *CrawlWorker) worker(workerID int) {
	defer w.wg.Done()

	w.logger.Info("Starting crawl worker", zap.Int("worker_id", workerID))

	for {
		select {
		case <-w.stopChan:
			w.logger.Info("Crawl worker stopping", zap.Int("worker_id", workerID))
			return
		default:
			// Process next job
			w.processNextJob(workerID)
		}
	}
}

// processNextJob processes the next job in the queue
func (w *CrawlWorker) processNextJob(workerID int) {
	ctx := context.Background()

	// Dequeue job with timeout
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	data, err := w.queue.Dequeue(ctx, w.config.QueueName)
	if err != nil {
		if err == queue.ErrQueueEmpty {
			// No jobs available, wait a bit
			time.Sleep(5 * time.Second)
			return
		}
		w.logger.Error("Failed to dequeue job", zap.Int("worker_id", workerID), zap.Error(err))
		time.Sleep(10 * time.Second)
		return
	}

	// Parse job data
	var crawlJob CrawlJob
	err = json.Unmarshal(data, &crawlJob)
	if err != nil {
		w.logger.Error("Failed to unmarshal crawl job", zap.Int("worker_id", workerID), zap.Error(err))
		return
	}

	w.logger.Info("Processing crawl job", 
		zap.Int("worker_id", workerID),
		zap.Uint("job_id", crawlJob.JobID),
		zap.Uint("tenant_id", crawlJob.TenantID))

	// Process the job
	err = w.processCrawlJob(ctx, crawlJob)
	if err != nil {
		w.logger.Error("Failed to process crawl job", 
			zap.Int("worker_id", workerID),
			zap.Uint("job_id", crawlJob.JobID),
			zap.Error(err))

		// Handle retry logic
		w.handleJobFailure(ctx, crawlJob, err)
		return
	}

	w.logger.Info("Crawl job processed successfully", 
		zap.Int("worker_id", workerID),
		zap.Uint("job_id", crawlJob.JobID))
}

// processCrawlJob processes a single crawl job
func (w *CrawlWorker) processCrawlJob(ctx context.Context, crawlJob CrawlJob) error {
	// Check if job exists and is valid
	job, err := w.crawlJobRepo.GetByID(ctx, crawlJob.TenantID, crawlJob.JobID)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if job == nil {
		return fmt.Errorf("crawl job not found: %d", crawlJob.JobID)
	}

	// Check if job can be started
	if !job.CanStart() {
		return fmt.Errorf("crawl job cannot be started, current status: %s", job.Status)
	}

	// Start crawling
	result, err := w.crawlService.StartCrawl(ctx, crawlJob.JobID)
	if err != nil {
		return fmt.Errorf("crawl execution failed: %w", err)
	}

	w.logger.Info("Crawl completed", 
		zap.Uint("job_id", crawlJob.JobID),
		zap.Int("pages_processed", result.PagesProcessed),
		zap.Int("articles_found", result.ArticlesFound),
		zap.Duration("duration", result.Duration))

	return nil
}

// handleJobFailure handles job failure and retry logic
func (w *CrawlWorker) handleJobFailure(ctx context.Context, crawlJob CrawlJob, jobErr error) {
	// Increment retry count
	err := w.crawlJobRepo.IncrementRetryCount(ctx, crawlJob.TenantID, crawlJob.JobID)
	if err != nil {
		w.logger.Error("Failed to increment retry count", 
			zap.Uint("job_id", crawlJob.JobID), 
			zap.Error(err))
	}

	// Get updated job to check retry count
	job, err := w.crawlJobRepo.GetByID(ctx, crawlJob.TenantID, crawlJob.JobID)
	if err != nil {
		w.logger.Error("Failed to get job for retry check", 
			zap.Uint("job_id", crawlJob.JobID), 
			zap.Error(err))
		return
	}

	if job == nil {
		return
	}

	// Check if we should retry
	if job.RetryCount < job.MaxRetries {
		w.logger.Info("Retrying crawl job", 
			zap.Uint("job_id", crawlJob.JobID),
			zap.Int("retry_count", job.RetryCount),
			zap.Int("max_retries", job.MaxRetries))

		// Re-enqueue with delay
		go func() {
			// Wait before retrying (exponential backoff)
			delay := time.Duration(job.RetryCount*job.RetryCount) * time.Minute
			if delay > 30*time.Minute {
				delay = 30 * time.Minute
			}
			time.Sleep(delay)

			// Re-enqueue
			crawlJob.RetryCount = job.RetryCount
			err := w.EnqueueJob(context.Background(), crawlJob.JobID, crawlJob.TenantID, crawlJob.Priority)
			if err != nil {
				w.logger.Error("Failed to re-enqueue job", 
					zap.Uint("job_id", crawlJob.JobID), 
					zap.Error(err))
			}
		}()
	} else {
		w.logger.Error("Crawl job failed permanently", 
			zap.Uint("job_id", crawlJob.JobID),
			zap.Int("retry_count", job.RetryCount),
			zap.Error(jobErr))

		// Update job status to failed
		err = w.crawlJobRepo.UpdateStatus(ctx, crawlJob.TenantID, crawlJob.JobID, string(internal.CrawlJobStatusFailed))
		if err != nil {
			w.logger.Error("Failed to update job status to failed", 
				zap.Uint("job_id", crawlJob.JobID), 
				zap.Error(err))
		}
	}
}

// GetQueueStats returns queue statistics
func (w *CrawlWorker) GetQueueStats(ctx context.Context) (map[string]interface{}, error) {
	stats, err := w.queue.GetStats(ctx, w.config.QueueName)
	if err != nil {
		return nil, fmt.Errorf("failed to get queue stats: %w", err)
	}

	return map[string]interface{}{
		"queue_name":     w.config.QueueName,
		"worker_count":   w.workers,
		"running":        w.IsRunning(),
		"queue_stats":    stats,
	}, nil
}

// PurgeQueue removes all jobs from the queue
func (w *CrawlWorker) PurgeQueue(ctx context.Context) error {
	w.logger.Info("Purging crawl queue")

	err := w.queue.Purge(ctx, w.config.QueueName)
	if err != nil {
		return fmt.Errorf("failed to purge queue: %w", err)
	}

	w.logger.Info("Crawl queue purged successfully")
	return nil
}

// GetQueueLength returns the number of jobs in the queue
func (w *CrawlWorker) GetQueueLength(ctx context.Context) (int, error) {
	length, err := w.queue.Length(ctx, w.config.QueueName)
	if err != nil {
		return 0, fmt.Errorf("failed to get queue length: %w", err)
	}

	return length, nil
}