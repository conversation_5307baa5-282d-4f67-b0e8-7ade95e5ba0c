package queue

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/repository"
	"wnapi/modules/crawl/services"
	"wnapi/pkg/queue"
)

// CrawlJob represents a crawl job in the queue
type CrawlJob struct {
	JobID     uint      `json:"job_id"`
	TenantID  uint      `json:"tenant_id"`
	UserID    uint      `json:"user_id"`
	Priority  int       `json:"priority"`
	RetryCount int      `json:"retry_count"`
	CreatedAt time.Time `json:"created_at"`
}

// CrawlWorker handles crawl job processing
type CrawlWorker struct {
	queue            queue.Queue
	crawlJobRepo     repository.CrawlJobRepository
	crawlService     *services.CrawlService
	config           *internal.CrawlConfig
	workers          int
	stopChan         chan struct{}
	wg               sync.WaitGroup
	running          bool
	mu               sync.RWMutex
}

// NewCrawlWorker creates a new crawl worker
func NewCrawlWorker(
	queue queue.Queue,
	crawlJobRepo repository.CrawlJobRepository,
	crawlService *services.CrawlService,
	config *internal.CrawlConfig,
) *CrawlWorker {
	return &CrawlWorker{
		queue:        queue,
		crawlJobRepo: crawlJobRepo,
		crawlService: crawlService,
		config:       config,
		workers:      config.QueueWorkerCount,
		stopChan:     make(chan struct{}),
	}
}

// Start starts the crawl workers
func (w *CrawlWorker) Start() error {
	w.mu.Lock()
	defer w.mu.Unlock()

	if w.running {
		return fmt.Errorf("crawl worker is already running")
	}

	fmt.Printf("Starting crawl workers with %d workers\n", w.workers)

	// Start worker goroutines
	for i := 0; i < w.workers; i++ {
		w.wg.Add(1)
		go w.worker(i)
	}

	w.running = true
	fmt.Println("Crawl workers started successfully")
	return nil
}

// Stop stops the crawl workers
func (w *CrawlWorker) Stop() {
	w.mu.Lock()
	defer w.mu.Unlock()

	if !w.running {
		return
	}

	fmt.Println("Stopping crawl workers")

	// Signal workers to stop
	close(w.stopChan)

	// Wait for all workers to finish
	w.wg.Wait()

	w.running = false
	fmt.Println("Crawl workers stopped")
}

// IsRunning returns whether the worker is running
func (w *CrawlWorker) IsRunning() bool {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.running
}

// EnqueueJob adds a crawl job to the queue
func (w *CrawlWorker) EnqueueJob(ctx context.Context, jobID, tenantID, userID uint, priority int) error {
	fmt.Printf("Enqueuing crawl job - JobID: %d, TenantID: %d, UserID: %d, Priority: %d\n", 
		jobID, tenantID, userID, priority)

	crawlJob := CrawlJob{
		JobID:     jobID,
		TenantID:  tenantID,
		UserID:    userID,
		Priority:  priority,
		RetryCount: 0,
		CreatedAt: time.Now(),
	}

	data, err := json.Marshal(crawlJob)
	if err != nil {
		return fmt.Errorf("failed to marshal crawl job: %w", err)
	}

	err = w.queue.Enqueue(ctx, w.config.QueueName, data, priority)
	if err != nil {
		return fmt.Errorf("failed to enqueue crawl job: %w", err)
	}

	fmt.Printf("Crawl job enqueued successfully - JobID: %d\n", jobID)
	return nil
}

// worker processes jobs from the queue
func (w *CrawlWorker) worker(workerID int) {
	defer w.wg.Done()

	fmt.Printf("Starting crawl worker %d\n", workerID)

	for {
		select {
		case <-w.stopChan:
			fmt.Printf("Crawl worker %d stopping\n", workerID)
			return
		default:
			// Process next job
			w.processNextJob(workerID)
		}
	}
}

// processNextJob processes the next job in the queue
func (w *CrawlWorker) processNextJob(workerID int) {
	ctx := context.Background()

	// Dequeue job with timeout
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	data, err := w.queue.Dequeue(ctx, w.config.QueueName)
	if err != nil {
		if err == queue.ErrQueueEmpty {
			// No jobs available, wait a bit
			time.Sleep(5 * time.Second)
			return
		}
		fmt.Printf("Worker %d: Failed to dequeue job: %v\n", workerID, err)
		time.Sleep(10 * time.Second)
		return
	}

	// Parse job data
	var crawlJob CrawlJob
	err = json.Unmarshal(data, &crawlJob)
	if err != nil {
		fmt.Printf("Worker %d: Failed to unmarshal crawl job: %v\n", workerID, err)
		return
	}

	fmt.Printf("Worker %d: Processing crawl job - JobID: %d, TenantID: %d\n", 
		workerID, crawlJob.JobID, crawlJob.TenantID)

	// Process the job
	err = w.processCrawlJob(ctx, crawlJob)
	if err != nil {
		fmt.Printf("Worker %d: Failed to process crawl job %d: %v\n", 
			workerID, crawlJob.JobID, err)

		// Handle retry logic
		w.handleJobFailure(ctx, crawlJob, err)
		return
	}

	fmt.Printf("Worker %d: Crawl job %d processed successfully\n", 
		workerID, crawlJob.JobID)
}

// processCrawlJob processes a single crawl job
func (w *CrawlWorker) processCrawlJob(ctx context.Context, crawlJob CrawlJob) error {
	// Check if job exists and is valid
	job, err := w.crawlJobRepo.GetByID(ctx, crawlJob.TenantID, crawlJob.UserID, crawlJob.JobID)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if job == nil {
		return fmt.Errorf("crawl job not found: %d", crawlJob.JobID)
	}

	// Check if job can be started
	if !job.CanStart() {
		return fmt.Errorf("crawl job cannot be started, current status: %s", job.Status)
	}

	// Start crawling
	result, err := w.crawlService.StartCrawl(ctx, crawlJob.TenantID, crawlJob.UserID, crawlJob.JobID)
	if err != nil {
		return fmt.Errorf("crawl execution failed: %w", err)
	}

	fmt.Printf("Crawl completed - JobID: %d, Pages: %d, Articles: %d, Duration: %v\n", 
		crawlJob.JobID, result.PagesProcessed, result.ArticlesFound, result.Duration)

	return nil
}

// handleJobFailure handles job failure and retry logic
func (w *CrawlWorker) handleJobFailure(ctx context.Context, crawlJob CrawlJob, jobErr error) {
	// Increment retry count
	err := w.crawlJobRepo.IncrementRetryCount(ctx, crawlJob.TenantID, crawlJob.UserID, crawlJob.JobID)
	if err != nil {
		fmt.Printf("Failed to increment retry count for job %d: %v\n", 
			crawlJob.JobID, err)
	}

	// Get updated job to check retry count
	job, err := w.crawlJobRepo.GetByID(ctx, crawlJob.TenantID, crawlJob.UserID, crawlJob.JobID)
	if err != nil {
		fmt.Printf("Failed to get job %d for retry check: %v\n", 
			crawlJob.JobID, err)
		return
	}

	if job == nil {
		return
	}

	// Check if we should retry
	if job.RetryCount < job.MaxRetries {
		fmt.Printf("Retrying crawl job %d (retry %d/%d)\n", 
			crawlJob.JobID, job.RetryCount, job.MaxRetries)

		// Re-enqueue with delay
		go func() {
			// Wait before retrying (exponential backoff)
			delay := time.Duration(job.RetryCount*job.RetryCount) * time.Minute
			if delay > 30*time.Minute {
				delay = 30 * time.Minute
			}
			time.Sleep(delay)

			// Re-enqueue
			crawlJob.RetryCount = job.RetryCount
			err := w.EnqueueJob(context.Background(), crawlJob.JobID, crawlJob.TenantID, crawlJob.UserID, crawlJob.Priority)
			if err != nil {
				fmt.Printf("Failed to re-enqueue job %d: %v\n", 
					crawlJob.JobID, err)
			}
		}()
	} else {
		fmt.Printf("Crawl job %d failed permanently after %d retries: %v\n", 
			crawlJob.JobID, job.RetryCount, jobErr)

		// Update job status to failed
		err = w.crawlJobRepo.UpdateStatus(ctx, crawlJob.TenantID, crawlJob.UserID, crawlJob.JobID, string(internal.CrawlJobStatusFailed))
		if err != nil {
			fmt.Printf("Failed to update job %d status to failed: %v\n", 
				crawlJob.JobID, err)
		}
	}
}

// GetQueueStats returns queue statistics
func (w *CrawlWorker) GetQueueStats(ctx context.Context) (map[string]interface{}, error) {
	stats, err := w.queue.GetStats(ctx, w.config.QueueName)
	if err != nil {
		return nil, fmt.Errorf("failed to get queue stats: %w", err)
	}

	return map[string]interface{}{
		"queue_name":     w.config.QueueName,
		"worker_count":   w.workers,
		"running":        w.IsRunning(),
		"queue_stats":    stats,
	}, nil
}

// PurgeQueue removes all jobs from the queue
func (w *CrawlWorker) PurgeQueue(ctx context.Context) error {
	fmt.Println("Purging crawl queue")

	err := w.queue.Purge(ctx, w.config.QueueName)
	if err != nil {
		return fmt.Errorf("failed to purge queue: %w", err)
	}

	fmt.Println("Crawl queue purged successfully")
	return nil
}

// GetQueueLength returns the number of jobs in the queue
func (w *CrawlWorker) GetQueueLength(ctx context.Context) (int, error) {
	length, err := w.queue.Length(ctx, w.config.QueueName)
	if err != nil {
		return 0, fmt.Errorf("failed to get queue length: %w", err)
	}

	return length, nil
}