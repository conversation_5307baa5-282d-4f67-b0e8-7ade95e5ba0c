package services

import (
	"context"
	"fmt"
	"time"

	"blog-api-v1/modules/crawl/request"
	"blog-api-v1/modules/crawl/response"
	"blog-api-v1/modules/crawl/internal"
	"blog-api-v1/modules/crawl/models"
	"blog-api-v1/modules/crawl/repository"
)

// CrawlJobService handles crawl job business logic
type CrawlJobService struct {
	crawlJobRepo repository.CrawlJobRepository
}

// NewCrawlJobService creates a new crawl job service
func NewCrawlJobService(
	crawlJobRepo repository.CrawlJobRepository,
) *CrawlJobService {
	return &CrawlJobService{
		crawlJobRepo: crawlJobRepo,
	}
}

// <PERSON><PERSON> creates a new crawl job
func (s *CrawlJobService) Create(ctx context.Context, tenantID, userID uint, req request.CreateCrawlJobRequest) (*models.CrawlJob, error) {
	fmt.Printf("Creating crawl job for tenant %d, user %d: %s\n", tenantID, userID, req.Name)

	// Create crawl job model
	crawlJob := &models.CrawlJob{
		TenantID:    tenantID,
		UserID:      userID,
		Name:        req.Name,
		Description: req.Description,
		URL:         req.URL,
		Status:      "pending",
		Rules:       req.Rules,
		Schedule:    req.Schedule,
		Active:      req.Active,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Create in database
	if err := s.crawlJobRepo.Create(ctx, tenantID, userID, crawlJob); err != nil {
		fmt.Printf("Failed to create crawl job: %v\n", err)
		return nil, fmt.Errorf("failed to create crawl job: %w", err)
	}

	fmt.Printf("Crawl job created successfully with ID: %d\n", crawlJob.ID)
	return crawlJob, nil
}

// GetByID gets a crawl job by ID
func (s *CrawlJobService) GetByID(ctx context.Context, tenantID, userID, id uint) (*response.CrawlJobResponse, error) {
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, userID, id)
	if err != nil {
		fmt.Printf("Failed to get crawl job %d: %v\n", id, err)
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return nil, nil
	}

	return &response.CrawlJobResponse{
		ID:          crawlJob.ID,
		TenantID:    crawlJob.TenantID,
		UserID:      crawlJob.UserID,
		Name:        crawlJob.Name,
		Description: crawlJob.Description,
		URL:         crawlJob.URL,
		Schedule:    crawlJob.Schedule,
		Active:      crawlJob.Active,
		Rules:       crawlJob.Rules,
		Status:      crawlJob.Status,
		CreatedAt:   crawlJob.CreatedAt,
		UpdatedAt:   crawlJob.UpdatedAt,
	}, nil
}

// Update updates a crawl job
func (s *CrawlJobService) Update(ctx context.Context, tenantID, userID, id uint, req request.UpdateCrawlJobRequest) (*models.CrawlJob, error) {
	fmt.Printf("Updating crawl job %d for tenant %d, user %d\n", id, tenantID, userID)

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, userID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return nil, fmt.Errorf("crawl job not found")
	}

	// Check if job is running
	if crawlJob.Status == "running" {
		return nil, fmt.Errorf("cannot update running crawl job")
	}

	// Update fields
	if req.Name != nil {
		crawlJob.Name = *req.Name
	}
	if req.Description != nil {
		crawlJob.Description = *req.Description
	}
	if req.URL != nil {
		crawlJob.URL = *req.URL
	}
	if req.Rules != nil {
		crawlJob.Rules = *req.Rules
	}
	if req.Schedule != nil {
		crawlJob.Schedule = *req.Schedule
	}
	if req.Active != nil {
		crawlJob.Active = *req.Active
	}

	crawlJob.UpdatedAt = time.Now()

	// Update in database
	if err := s.crawlJobRepo.Update(ctx, tenantID, userID, crawlJob); err != nil {
		fmt.Printf("Failed to update crawl job: %v\n", err)
		return nil, fmt.Errorf("failed to update crawl job: %w", err)
	}

	fmt.Printf("Crawl job updated successfully: %d\n", crawlJob.ID)
	return crawlJob, nil
}

// Delete deletes a crawl job
func (s *CrawlJobService) Delete(ctx context.Context, tenantID, userID, id uint) error {
	fmt.Printf("Deleting crawl job %d for tenant %d, user %d\n", id, tenantID, userID)

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, userID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return fmt.Errorf("crawl job not found")
	}

	// Check if job is running
	if crawlJob.Status == "running" {
		return fmt.Errorf("cannot delete running crawl job")
	}

	// Delete from database
	if err := s.crawlJobRepo.Delete(ctx, tenantID, userID, id); err != nil {
		fmt.Printf("Failed to delete crawl job: %v\n", err)
		return fmt.Errorf("failed to delete crawl job: %w", err)
	}

	fmt.Printf("Crawl job deleted successfully: %d\n", id)
	return nil
}

// List lists crawl jobs with pagination
func (s *CrawlJobService) List(ctx context.Context, tenantID, userID uint, req request.ListCrawlJobRequest) (*response.CrawlJobListResponse, error) {
	fmt.Printf("Listing crawl jobs for tenant %d, user %d\n", tenantID, userID)

	// Set default pagination
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.Limit <= 0 {
		req.Limit = 10
	}

	// Calculate offset
	offset := (req.Page - 1) * req.Limit

	// Get crawl jobs
	crawlJobs, total, err := s.crawlJobRepo.List(ctx, tenantID, userID, req.Status, req.Search, req.Limit, offset)
	if err != nil {
		fmt.Printf("Failed to list crawl jobs: %v\n", err)
		return nil, fmt.Errorf("failed to list crawl jobs: %w", err)
	}

	// Convert to response format
	responses := make([]*response.CrawlJobResponse, len(crawlJobs))
	for i, job := range crawlJobs {
		responses[i] = &response.CrawlJobResponse{
			ID:          job.ID,
			TenantID:    job.TenantID,
			UserID:      job.UserID,
			Name:        job.Name,
			Description: job.Description,
			URL:         job.URL,
			Schedule:    job.Schedule,
			Active:      job.Active,
			Rules:       job.Rules,
			Status:      job.Status,
			CreatedAt:   job.CreatedAt,
			UpdatedAt:   job.UpdatedAt,
		}
	}

	return &response.CrawlJobListResponse{
		CrawlJobs: responses,
		Total:     total,
		Page:      req.Page,
		Limit:     req.Limit,
	}, nil
}

// Start starts a crawl job
func (s *CrawlJobService) Start(ctx context.Context, tenantID, userID, id uint, req request.StartCrawlJobRequest) error {
	fmt.Printf("Starting crawl job %d for tenant %d, user %d\n", id, tenantID, userID)

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, userID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return fmt.Errorf("crawl job not found")
	}

	// Check if job can be started
	if !req.Force && crawlJob.Status == "running" {
		return fmt.Errorf("crawl job is already running")
	}

	// Update status to running
	if err := s.crawlJobRepo.UpdateStatus(ctx, tenantID, userID, id, "running"); err != nil {
		fmt.Printf("Failed to update crawl job status: %v\n", err)
		return fmt.Errorf("failed to update crawl job status: %w", err)
	}

	// TODO: Queue the crawl job for processing
	// This would typically involve sending a message to a queue system
	// For now, we'll just log it
	fmt.Printf("Crawl job queued for processing: %d\n", id)

	return nil
}

// Stop stops a crawl job
func (s *CrawlJobService) Stop(ctx context.Context, tenantID, userID, id uint, req request.StopCrawlJobRequest) error {
	fmt.Printf("Stopping crawl job %d for tenant %d, user %d\n", id, tenantID, userID)

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, userID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return fmt.Errorf("crawl job not found")
	}

	// Check if job can be stopped
	if crawlJob.Status != "running" {
		return fmt.Errorf("crawl job is not running")
	}

	// Update status to cancelled
	if err := s.crawlJobRepo.UpdateStatus(ctx, tenantID, userID, id, "cancelled"); err != nil {
		fmt.Printf("Failed to update crawl job status: %v\n", err)
		return fmt.Errorf("failed to update crawl job status: %w", err)
	}

	// TODO: Send stop signal to the running crawler
	// This would typically involve sending a message to stop the crawler
	fmt.Printf("Stop signal sent to crawl job %d, reason: %s\n", id, req.Reason)

	return nil
}

// UpdateStatus updates the status of a crawl job
func (s *CrawlJobService) UpdateStatus(ctx context.Context, tenantID, userID, id uint, req request.UpdateCrawlJobStatusRequest) error {
	fmt.Printf("Updating crawl job status %d for tenant %d, user %d to %s\n", id, tenantID, userID, req.Status)

	return s.crawlJobRepo.UpdateStatus(ctx, tenantID, userID, id, string(req.Status))
}

// UpdateProgress updates the progress of a crawl job
func (s *CrawlJobService) UpdateProgress(ctx context.Context, tenantID, userID, id uint, req request.UpdateCrawlJobProgressRequest) error {
	fmt.Printf("Updating crawl job progress %d for tenant %d, user %d\n", id, tenantID, userID)
	return s.crawlJobRepo.UpdateProgress(ctx, tenantID, userID, id, req.Progress)
}

// GetStats gets statistics for crawl jobs
func (s *CrawlJobService) GetStats(ctx context.Context, tenantID, userID uint) (*response.CrawlJobStatsResponse, error) {
	fmt.Printf("Getting crawl job stats for tenant %d, user %d\n", tenantID, userID)
	return s.crawlJobRepo.GetStats(ctx, tenantID, userID)
}

// GetActiveJobs gets active crawl jobs
func (s *CrawlJobService) GetActiveJobs(ctx context.Context, tenantID, userID uint) ([]*models.CrawlJob, error) {
	fmt.Printf("Getting active crawl jobs for tenant %d, user %d\n", tenantID, userID)
	return s.crawlJobRepo.GetActiveJobs(ctx, tenantID, userID)
}

// GetScheduledJobs gets scheduled crawl jobs that are due to run
func (s *CrawlJobService) GetScheduledJobs(ctx context.Context, tenantID, userID uint) ([]*models.CrawlJob, error) {
	fmt.Printf("Getting scheduled crawl jobs for tenant %d, user %d\n", tenantID, userID)
	return s.crawlJobRepo.GetScheduledJobs(ctx, tenantID, userID)
}

// Clone clones a crawl job
func (s *CrawlJobService) Clone(ctx context.Context, tenantID, userID, id uint, req request.CloneCrawlJobRequest) (*models.CrawlJob, error) {
	fmt.Printf("Cloning crawl job %d for tenant %d, user %d\n", id, tenantID, userID)

	// Get existing crawl job
	originalJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, userID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if originalJob == nil {
		return nil, fmt.Errorf("crawl job not found")
	}

	// Create new crawl job based on original
	newJob := &models.CrawlJob{
		TenantID:    tenantID,
		UserID:      userID,
		Name:        req.Name,
		Description: req.Description,
		URL:         originalJob.URL,
		Status:      "pending",
		Rules:       originalJob.Rules,
		Schedule:    originalJob.Schedule,
		Active:      req.Active,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// Create in database
	if err := s.crawlJobRepo.Create(ctx, tenantID, userID, newJob); err != nil {
		fmt.Printf("Failed to clone crawl job: %v\n", err)
		return nil, fmt.Errorf("failed to clone crawl job: %w", err)
	}

	fmt.Printf("Crawl job cloned successfully: original %d, new %d\n", id, newJob.ID)
	return newJob, nil
}

// BulkUpdate performs bulk operations on crawl jobs
func (s *CrawlJobService) BulkUpdate(ctx context.Context, tenantID, userID uint, req request.BulkUpdateCrawlJobRequest) (*response.BulkOperationResponse, error) {
	fmt.Printf("Performing bulk operation %s on crawl jobs for tenant %d, user %d, count: %d\n", req.Action, tenantID, userID, len(req.IDs))

	response := &response.BulkOperationResponse{
		Success:   true,
		Processed: 0,
		Failed:    0,
		Errors:    []string{},
	}

	for _, id := range req.IDs {
		var err error
		switch req.Action {
		case "start":
			err = s.Start(ctx, tenantID, userID, id, request.StartCrawlJobRequest{})
		case "stop":
			err = s.Stop(ctx, tenantID, userID, id, request.StopCrawlJobRequest{Reason: req.Reason})
		case "activate":
			_, err = s.Update(ctx, tenantID, userID, id, request.UpdateCrawlJobRequest{Active: &[]bool{true}[0]})
		case "deactivate":
			_, err = s.Update(ctx, tenantID, userID, id, request.UpdateCrawlJobRequest{Active: &[]bool{false}[0]})
		case "delete":
			err = s.Delete(ctx, tenantID, userID, id)
		default:
			err = fmt.Errorf("unknown action: %s", req.Action)
		}

		if err != nil {
			response.Failed++
			response.Errors = append(response.Errors, fmt.Sprintf("ID %d: %s", id, err.Error()))
		} else {
			response.Processed++
		}
	}

	if response.Failed > 0 {
		response.Success = false
		response.Message = fmt.Sprintf("Bulk operation completed with %d failures", response.Failed)
	} else {
		response.Message = fmt.Sprintf("Bulk operation completed successfully for %d items", response.Processed)
	}

	return response, nil
}