package services

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"

	"wnapi/modules/crawl/dto/request"
	"wnapi/modules/crawl/dto/response"
	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/models"
	"wnapi/modules/crawl/repository"
	"wnapi/pkg/logger"
	"wnapi/pkg/utils"
)

// CrawlJobService handles crawl job business logic
type CrawlJobService struct {
	crawlJobRepo repository.CrawlJobRepository
	logger       *zap.Logger
}

// NewCrawlJobService creates a new crawl job service
func NewCrawlJobService(
	crawlJobRepo repository.CrawlJobRepository,
	logger *zap.Logger,
) *CrawlJobService {
	return &CrawlJobService{
		crawlJobRepo: crawlJobRepo,
		logger:       logger,
	}
}

// Create creates a new crawl job
func (s *CrawlJobService) Create(ctx context.Context, tenantID, userID uint, req request.CreateCrawlJobRequest) (*models.CrawlJob, error) {
	s.logger.Info("Creating crawl job", zap.Uint("tenant_id", tenantID), zap.String("name", req.Name))

	// Create crawl job model
	crawlJob := &models.CrawlJob{
		TenantID:    tenantID,
		WebsiteID:   req.WebsiteID,
		Name:        req.Name,
		Description: req.Description,
		StartURL:    req.StartURL,
		Type:        req.Type,
		Status:      internal.CrawlJobStatusPending,
		Rules:       models.CrawlRuleJSON(req.Rules),
		Progress:    models.CrawlProgressJSON{},
		Schedule:    req.Schedule,
		Active:      req.Active,
		MaxRetries:  req.MaxRetries,
		CreatedBy:   userID,
	}

	// Set default values
	if crawlJob.MaxRetries == 0 {
		crawlJob.MaxRetries = 3
	}

	// Calculate next run time if schedule is provided
	if crawlJob.Schedule != "" && crawlJob.Active {
		nextRun, err := utils.ParseCronExpression(crawlJob.Schedule)
		if err != nil {
			s.logger.Error("Invalid cron expression", zap.Error(err), zap.String("schedule", crawlJob.Schedule))
			return nil, fmt.Errorf("invalid cron expression: %w", err)
		}
		crawlJob.NextRunAt = &nextRun
	}

	// Create in database
	if err := s.crawlJobRepo.Create(ctx, crawlJob); err != nil {
		s.logger.Error("Failed to create crawl job", zap.Error(err))
		return nil, fmt.Errorf("failed to create crawl job: %w", err)
	}

	s.logger.Info("Crawl job created successfully", zap.Uint("id", crawlJob.ID))
	return crawlJob, nil
}

// GetByID gets a crawl job by ID
func (s *CrawlJobService) GetByID(ctx context.Context, tenantID, id uint) (*response.CrawlJobResponse, error) {
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		s.logger.Error("Failed to get crawl job", zap.Error(err), zap.Uint("id", id))
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return nil, nil
	}

	return &response.CrawlJobResponse{
		CrawlJob: crawlJob,
		CanStart: crawlJob.CanStart(),
		CanStop:  crawlJob.CanStop(),
	}, nil
}

// Update updates a crawl job
func (s *CrawlJobService) Update(ctx context.Context, tenantID, id uint, req request.UpdateCrawlJobRequest) (*models.CrawlJob, error) {
	s.logger.Info("Updating crawl job", zap.Uint("tenant_id", tenantID), zap.Uint("id", id))

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return nil, fmt.Errorf("crawl job not found")
	}

	// Check if job is running
	if crawlJob.IsRunning() {
		return nil, fmt.Errorf("cannot update running crawl job")
	}

	// Update fields
	if req.Name != nil {
		crawlJob.Name = *req.Name
	}
	if req.Description != nil {
		crawlJob.Description = *req.Description
	}
	if req.StartURL != nil {
		crawlJob.StartURL = *req.StartURL
	}
	if req.Type != nil {
		crawlJob.Type = *req.Type
	}
	if req.Rules != nil {
		crawlJob.Rules = models.CrawlRuleJSON(*req.Rules)
	}
	if req.Schedule != nil {
		crawlJob.Schedule = *req.Schedule
		// Recalculate next run time
		if crawlJob.Schedule != "" && crawlJob.Active {
			nextRun, err := utils.ParseCronExpression(crawlJob.Schedule)
			if err != nil {
				return nil, fmt.Errorf("invalid cron expression: %w", err)
			}
			crawlJob.NextRunAt = &nextRun
		} else {
			crawlJob.NextRunAt = nil
		}
	}
	if req.Active != nil {
		crawlJob.Active = *req.Active
		// Update next run time based on active status
		if !crawlJob.Active {
			crawlJob.NextRunAt = nil
		} else if crawlJob.Schedule != "" {
			nextRun, err := utils.ParseCronExpression(crawlJob.Schedule)
			if err == nil {
				crawlJob.NextRunAt = &nextRun
			}
		}
	}
	if req.MaxRetries != nil {
		crawlJob.MaxRetries = *req.MaxRetries
	}

	// Update in database
	if err := s.crawlJobRepo.Update(ctx, crawlJob); err != nil {
		s.logger.Error("Failed to update crawl job", zap.Error(err))
		return nil, fmt.Errorf("failed to update crawl job: %w", err)
	}

	s.logger.Info("Crawl job updated successfully", zap.Uint("id", crawlJob.ID))
	return crawlJob, nil
}

// Delete deletes a crawl job
func (s *CrawlJobService) Delete(ctx context.Context, tenantID, id uint) error {
	s.logger.Info("Deleting crawl job", zap.Uint("tenant_id", tenantID), zap.Uint("id", id))

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return fmt.Errorf("crawl job not found")
	}

	// Check if job is running
	if crawlJob.IsRunning() {
		return fmt.Errorf("cannot delete running crawl job")
	}

	// Delete from database
	if err := s.crawlJobRepo.Delete(ctx, tenantID, id); err != nil {
		s.logger.Error("Failed to delete crawl job", zap.Error(err))
		return fmt.Errorf("failed to delete crawl job: %w", err)
	}

	s.logger.Info("Crawl job deleted successfully", zap.Uint("id", id))
	return nil
}

// List lists crawl jobs with pagination
func (s *CrawlJobService) List(ctx context.Context, tenantID, websiteID uint, req request.ListCrawlJobRequest) (*response.CrawlJobListResponse, error) {
	return s.crawlJobRepo.List(ctx, tenantID, websiteID, req)
}

// Start starts a crawl job
func (s *CrawlJobService) Start(ctx context.Context, tenantID, id uint, req request.StartCrawlJobRequest) error {
	s.logger.Info("Starting crawl job", zap.Uint("tenant_id", tenantID), zap.Uint("id", id))

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return fmt.Errorf("crawl job not found")
	}

	// Check if job can be started
	if !req.Force && !crawlJob.CanStart() {
		return fmt.Errorf("crawl job cannot be started in current state: %s", crawlJob.Status)
	}

	// Update status to running
	if err := s.crawlJobRepo.UpdateStatus(ctx, tenantID, id, string(internal.CrawlJobStatusRunning)); err != nil {
		s.logger.Error("Failed to update crawl job status", zap.Error(err))
		return fmt.Errorf("failed to update crawl job status: %w", err)
	}

	// TODO: Queue the crawl job for processing
	// This would typically involve sending a message to a queue system
	// For now, we'll just log it
	s.logger.Info("Crawl job queued for processing", zap.Uint("id", id))

	return nil
}

// Stop stops a crawl job
func (s *CrawlJobService) Stop(ctx context.Context, tenantID, id uint, req request.StopCrawlJobRequest) error {
	s.logger.Info("Stopping crawl job", zap.Uint("tenant_id", tenantID), zap.Uint("id", id))

	// Get existing crawl job
	crawlJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl job: %w", err)
	}

	if crawlJob == nil {
		return fmt.Errorf("crawl job not found")
	}

	// Check if job can be stopped
	if !crawlJob.CanStop() {
		return fmt.Errorf("crawl job cannot be stopped in current state: %s", crawlJob.Status)
	}

	// Update status to cancelled
	if err := s.crawlJobRepo.UpdateStatus(ctx, tenantID, id, string(internal.CrawlJobStatusCancelled)); err != nil {
		s.logger.Error("Failed to update crawl job status", zap.Error(err))
		return fmt.Errorf("failed to update crawl job status: %w", err)
	}

	// TODO: Send stop signal to the running crawler
	// This would typically involve sending a message to stop the crawler
	s.logger.Info("Stop signal sent to crawl job", zap.Uint("id", id), zap.String("reason", req.Reason))

	return nil
}

// UpdateStatus updates the status of a crawl job
func (s *CrawlJobService) UpdateStatus(ctx context.Context, tenantID, id uint, req request.UpdateCrawlJobStatusRequest) error {
	s.logger.Info("Updating crawl job status", 
		zap.Uint("tenant_id", tenantID), 
		zap.Uint("id", id), 
		zap.String("status", string(req.Status)))

	return s.crawlJobRepo.UpdateStatus(ctx, tenantID, id, string(req.Status))
}

// UpdateProgress updates the progress of a crawl job
func (s *CrawlJobService) UpdateProgress(ctx context.Context, tenantID, id uint, req request.UpdateCrawlJobProgressRequest) error {
	return s.crawlJobRepo.UpdateProgress(ctx, tenantID, id, models.CrawlProgressJSON(req.Progress))
}

// GetStats gets statistics for crawl jobs
func (s *CrawlJobService) GetStats(ctx context.Context, tenantID, websiteID uint) (*response.CrawlJobStatsResponse, error) {
	return s.crawlJobRepo.GetStats(ctx, tenantID, websiteID)
}

// GetActiveJobs gets active crawl jobs
func (s *CrawlJobService) GetActiveJobs(ctx context.Context, tenantID uint) ([]*models.CrawlJob, error) {
	return s.crawlJobRepo.GetActiveJobs(ctx, tenantID)
}

// GetScheduledJobs gets scheduled crawl jobs that are due to run
func (s *CrawlJobService) GetScheduledJobs(ctx context.Context) ([]*models.CrawlJob, error) {
	return s.crawlJobRepo.GetScheduledJobs(ctx)
}

// Clone clones a crawl job
func (s *CrawlJobService) Clone(ctx context.Context, tenantID, id, userID uint, req request.CloneCrawlJobRequest) (*models.CrawlJob, error) {
	s.logger.Info("Cloning crawl job", zap.Uint("tenant_id", tenantID), zap.Uint("id", id))

	// Get existing crawl job
	originalJob, err := s.crawlJobRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl job: %w", err)
	}

	if originalJob == nil {
		return nil, fmt.Errorf("crawl job not found")
	}

	// Create new crawl job based on original
	newJob := &models.CrawlJob{
		TenantID:    tenantID,
		WebsiteID:   originalJob.WebsiteID,
		Name:        req.Name,
		Description: req.Description,
		StartURL:    originalJob.StartURL,
		Type:        originalJob.Type,
		Status:      internal.CrawlJobStatusPending,
		Rules:       originalJob.Rules,
		Progress:    models.CrawlProgressJSON{},
		Schedule:    originalJob.Schedule,
		Active:      req.Active,
		MaxRetries:  originalJob.MaxRetries,
		CreatedBy:   userID,
	}

	// Calculate next run time if schedule is provided and job is active
	if newJob.Schedule != "" && newJob.Active {
		nextRun, err := utils.ParseCronExpression(newJob.Schedule)
		if err == nil {
			newJob.NextRunAt = &nextRun
		}
	}

	// Create in database
	if err := s.crawlJobRepo.Create(ctx, newJob); err != nil {
		s.logger.Error("Failed to clone crawl job", zap.Error(err))
		return nil, fmt.Errorf("failed to clone crawl job: %w", err)
	}

	s.logger.Info("Crawl job cloned successfully", zap.Uint("original_id", id), zap.Uint("new_id", newJob.ID))
	return newJob, nil
}

// BulkUpdate performs bulk operations on crawl jobs
func (s *CrawlJobService) BulkUpdate(ctx context.Context, tenantID uint, req request.BulkUpdateCrawlJobRequest) (*response.BulkOperationResponse, error) {
	s.logger.Info("Performing bulk operation on crawl jobs", 
		zap.Uint("tenant_id", tenantID), 
		zap.String("action", req.Action), 
		zap.Int("count", len(req.IDs)))

	response := &response.BulkOperationResponse{
		Success:   true,
		Processed: 0,
		Failed:    0,
		Errors:    []string{},
	}

	for _, id := range req.IDs {
		var err error
		switch req.Action {
		case "start":
			err = s.Start(ctx, tenantID, id, request.StartCrawlJobRequest{})
		case "stop":
			err = s.Stop(ctx, tenantID, id, request.StopCrawlJobRequest{Reason: req.Reason})
		case "activate":
			_, err = s.Update(ctx, tenantID, id, request.UpdateCrawlJobRequest{Active: &[]bool{true}[0]})
		case "deactivate":
			_, err = s.Update(ctx, tenantID, id, request.UpdateCrawlJobRequest{Active: &[]bool{false}[0]})
		case "delete":
			err = s.Delete(ctx, tenantID, id)
		default:
			err = fmt.Errorf("unknown action: %s", req.Action)
		}

		if err != nil {
			response.Failed++
			response.Errors = append(response.Errors, fmt.Sprintf("ID %d: %s", id, err.Error()))
		} else {
			response.Processed++
		}
	}

	if response.Failed > 0 {
		response.Success = false
		response.Message = fmt.Sprintf("Bulk operation completed with %d failures", response.Failed)
	} else {
		response.Message = fmt.Sprintf("Bulk operation completed successfully for %d items", response.Processed)
	}

	return response, nil
}