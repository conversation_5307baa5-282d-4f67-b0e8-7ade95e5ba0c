package services

import (
	"context"
	"crypto/md5"
	"fmt"
	"strings"
	"time"

	"blog-api-v1/modules/crawl/request"
	"blog-api-v1/modules/crawl/response"
	"blog-api-v1/modules/crawl/models"
	"blog-api-v1/modules/crawl/repository"
	"wnapi/pkg/utils"
)

// CrawlArticleService handles crawl article business logic
type CrawlArticleService struct {
	crawlArticleRepo repository.CrawlArticleRepository
}

// NewCrawlArticleService creates a new crawl article service
func NewCrawlArticleService(
	crawlArticleRepo repository.CrawlArticleRepository,
) *CrawlArticleService {
	return &CrawlArticleService{
		crawlArticleRepo: crawlArticleRepo,
	}
}

// Create creates a new crawl article
func (s *CrawlArticleService) Create(ctx context.Context, tenantID, userID uint, req request.CreateCrawlArticleRequest) (*models.CrawlArticle, error) {
	fmt.Printf("Creating crawl article for tenant %d, user %d: %s\n", tenantID, userID, req.Title)

	// Generate slug if not provided
	slug := req.Slug
	if slug == "" {
		slug = utils.GenerateSlug(req.Title)
	}

	// Calculate word count
	wordCount := s.calculateWordCount(req.Content)

	// Generate content hash for duplicate detection
	hashContent := s.generateContentHash(req.Content)

	// Check for duplicates
	existingArticle, err := s.crawlArticleRepo.GetByHashContent(ctx, tenantID, userID, hashContent)
	if err != nil {
		fmt.Printf("Failed to check for duplicate content: %v\n", err)
		return nil, fmt.Errorf("failed to check for duplicate content: %w", err)
	}

	if existingArticle != nil {
		fmt.Printf("Duplicate content detected for URL %s, existing ID: %d\n", req.URL, existingArticle.ID)
		return nil, fmt.Errorf("duplicate content detected")
	}

	// Create crawl article model
	article := &models.CrawlArticle{
		TenantID:     tenantID,
		UserID:       userID,
		CrawlJobID:   req.CrawlJobID,
		Title:        req.Title,
		Slug:         slug,
		Content:      req.Content,
		Summary:      req.Summary,
		URL:          req.URL,
		ImageURL:     req.ImageURL,
		Author:       req.Author,
		PublishedAt:  req.PublishedAt,
		Status:       "pending",
		Metadata:     req.Metadata,
		Tags:         req.Tags,
		Categories:   req.Categories,
		WordCount:    wordCount,
		Language:     req.Language,
		HashContent:  hashContent,
		RetryCount:   0,
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	// Set default language if not provided
	if article.Language == "" {
		article.Language = "en"
	}

	// Create in database
	if err := s.crawlArticleRepo.Create(ctx, tenantID, userID, article); err != nil {
		fmt.Printf("Failed to create crawl article: %v\n", err)
		return nil, fmt.Errorf("failed to create crawl article: %w", err)
	}

	fmt.Printf("Crawl article created successfully: %d\n", article.ID)
	return article, nil
}

// GetByID gets a crawl article by ID
func (s *CrawlArticleService) GetByID(ctx context.Context, tenantID, userID, id uint) (*response.CrawlArticleResponse, error) {
	article, err := s.crawlArticleRepo.GetByID(ctx, tenantID, userID, id)
	if err != nil {
		fmt.Printf("Failed to get crawl article %d: %v\n", id, err)
		return nil, fmt.Errorf("failed to get crawl article: %w", err)
	}

	if article == nil {
		return nil, nil
	}

	return &response.CrawlArticleResponse{
		ID:          article.ID,
		TenantID:    article.TenantID,
		UserID:      article.UserID,
		CrawlJobID:  article.CrawlJobID,
		Title:       article.Title,
		Slug:        article.Slug,
		Content:     article.Content,
		Summary:     article.Summary,
		URL:         article.URL,
		ImageURL:    article.ImageURL,
		Author:      article.Author,
		PublishedAt: article.PublishedAt,
		Status:      article.Status,
		Metadata:    article.Metadata,
		Tags:        article.Tags,
		Categories:  article.Categories,
		WordCount:   article.WordCount,
		Language:    article.Language,
		CreatedAt:   article.CreatedAt,
		UpdatedAt:   article.UpdatedAt,
	}, nil
}

// Update updates a crawl article
func (s *CrawlArticleService) Update(ctx context.Context, tenantID, userID, id uint, req request.UpdateCrawlArticleRequest) (*models.CrawlArticle, error) {
	fmt.Printf("Updating crawl article %d for tenant %d, user %d\n", id, tenantID, userID)

	// Get existing crawl article
	article, err := s.crawlArticleRepo.GetByID(ctx, tenantID, userID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl article: %w", err)
	}

	if article == nil {
		return nil, fmt.Errorf("crawl article not found")
	}

	// Update fields
	if req.Title != nil {
		article.Title = *req.Title
		// Regenerate slug if title changed
		if req.Slug == nil {
			article.Slug = utils.GenerateSlug(*req.Title)
		}
	}
	if req.Slug != nil {
		article.Slug = *req.Slug
	}
	if req.Content != nil {
		article.Content = *req.Content
		// Recalculate word count and hash
		article.WordCount = s.calculateWordCount(*req.Content)
		article.HashContent = s.generateContentHash(*req.Content)
	}
	if req.Summary != nil {
		article.Summary = *req.Summary
	}
	if req.ImageURL != nil {
		article.ImageURL = *req.ImageURL
	}
	if req.Author != nil {
		article.Author = *req.Author
	}
	if req.PublishedAt != nil {
		article.PublishedAt = req.PublishedAt
	}
	if req.Metadata != nil {
		article.Metadata = *req.Metadata
	}
	if req.Tags != nil {
		article.Tags = req.Tags
	}
	if req.Categories != nil {
		article.Categories = req.Categories
	}
	if req.Language != nil {
		article.Language = *req.Language
	}

	article.UpdatedAt = time.Now()

	// Update in database
	if err := s.crawlArticleRepo.Update(ctx, tenantID, userID, article); err != nil {
		fmt.Printf("Failed to update crawl article: %v\n", err)
		return nil, fmt.Errorf("failed to update crawl article: %w", err)
	}

	fmt.Printf("Crawl article updated successfully: %d\n", article.ID)
	return article, nil
}

// Delete deletes a crawl article
func (s *CrawlArticleService) Delete(ctx context.Context, tenantID, userID, id uint) error {
	fmt.Printf("Deleting crawl article %d for tenant %d, user %d\n", id, tenantID, userID)

	// Delete from database
	if err := s.crawlArticleRepo.Delete(ctx, tenantID, userID, id); err != nil {
		fmt.Printf("Failed to delete crawl article: %v\n", err)
		return fmt.Errorf("failed to delete crawl article: %w", err)
	}

	fmt.Printf("Crawl article deleted successfully: %d\n", id)
	return nil
}

// List lists crawl articles with pagination
func (s *CrawlArticleService) List(ctx context.Context, tenantID, userID uint, req request.ListCrawlArticleRequest) (*response.CrawlArticleListResponse, error) {
	fmt.Printf("Listing crawl articles for tenant %d, user %d\n", tenantID, userID)
	return s.crawlArticleRepo.List(ctx, tenantID, userID, req)
}

// Search searches crawl articles with advanced filters
func (s *CrawlArticleService) Search(ctx context.Context, tenantID, userID uint, req request.SearchCrawlArticleRequest) (*response.CrawlArticleListResponse, error) {
	fmt.Printf("Searching crawl articles for tenant %d, user %d\n", tenantID, userID)
	return s.crawlArticleRepo.Search(ctx, tenantID, userID, req)
}

// UpdateStatus updates the status of a crawl article
func (s *CrawlArticleService) UpdateStatus(ctx context.Context, tenantID, userID, id uint, req request.UpdateCrawlArticleStatusRequest) error {
	fmt.Printf("Updating crawl article status %d for tenant %d, user %d to %s\n", id, tenantID, userID, req.Status)

	return s.crawlArticleRepo.UpdateStatus(ctx, tenantID, userID, id, string(req.Status))
}

// GetStats gets statistics for crawl articles
func (s *CrawlArticleService) GetStats(ctx context.Context, tenantID, userID uint) (*response.CrawlArticleStatsResponse, error) {
	fmt.Printf("Getting crawl article stats for tenant %d, user %d\n", tenantID, userID)
	return s.crawlArticleRepo.GetStats(ctx, tenantID, userID)
}

// GetByCrawlJobID gets crawl articles by crawl job ID
func (s *CrawlArticleService) GetByCrawlJobID(ctx context.Context, tenantID, userID, crawlJobID uint) ([]*models.CrawlArticle, error) {
	fmt.Printf("Getting crawl articles by job ID %d for tenant %d, user %d\n", crawlJobID, tenantID, userID)
	return s.crawlArticleRepo.GetByCrawlJobID(ctx, tenantID, userID, crawlJobID)
}

// CheckDuplicate checks if an article is a duplicate
func (s *CrawlArticleService) CheckDuplicate(ctx context.Context, tenantID, userID uint, req request.DuplicateCheckRequest) (*response.DuplicateCheckResponse, error) {
	fmt.Printf("Checking for duplicate article for tenant %d, user %d\n", tenantID, userID)

	response := &response.DuplicateCheckResponse{
		IsDuplicate: false,
		Similarity:  0.0,
	}

	// Check by URL if provided
	if req.URL != "" {
		existingArticle, err := s.crawlArticleRepo.GetByURL(ctx, tenantID, userID, req.URL)
		if err != nil {
			return nil, fmt.Errorf("failed to check URL duplicate: %w", err)
		}
		if existingArticle != nil {
			response.IsDuplicate = true
			response.Similarity = 1.0
			response.MatchedArticle = existingArticle
			response.Message = "Exact URL match found"
			return response, nil
		}
	}

	// Check by content hash if provided
	if req.HashContent != "" {
		existingArticle, err := s.crawlArticleRepo.GetByHashContent(ctx, tenantID, userID, req.HashContent)
		if err != nil {
			return nil, fmt.Errorf("failed to check content hash duplicate: %w", err)
		}
		if existingArticle != nil {
			response.IsDuplicate = true
			response.Similarity = 1.0
			response.MatchedArticle = existingArticle
			response.Message = "Exact content match found"
			return response, nil
		}
	}

	// Check by content similarity if content is provided
	if req.Content != "" {
		hashContent := s.generateContentHash(req.Content)
		existingArticle, err := s.crawlArticleRepo.GetByHashContent(ctx, tenantID, userID, hashContent)
		if err != nil {
			return nil, fmt.Errorf("failed to check content duplicate: %w", err)
		}
		if existingArticle != nil {
			response.IsDuplicate = true
			response.Similarity = 1.0
			response.MatchedArticle = existingArticle
			response.Message = "Content hash match found"
			return response, nil
		}
	}

	// TODO: Implement fuzzy matching for title similarity
	// This would involve more complex algorithms like Levenshtein distance

	response.Message = "No duplicates found"
	return response, nil
}

// BulkUpdate performs bulk operations on crawl articles
func (s *CrawlArticleService) BulkUpdate(ctx context.Context, tenantID, userID uint, req request.BulkUpdateCrawlArticleRequest) (*response.BulkOperationResponse, error) {
	fmt.Printf("Performing bulk operation on crawl articles for tenant %d, user %d: %s (%d items)\n", tenantID, userID, req.Action, len(req.IDs))

	response := &response.BulkOperationResponse{
		Success:   true,
		Processed: 0,
		Failed:    0,
		Errors:    []string{},
	}

	switch req.Action {
	case "process":
		err := s.crawlArticleRepo.BatchUpdateStatus(ctx, tenantID, userID, req.IDs, "processed")
		if err != nil {
			response.Success = false
			response.Failed = len(req.IDs)
			response.Errors = append(response.Errors, err.Error())
		} else {
			response.Processed = len(req.IDs)
		}
	case "retry":
		err := s.crawlArticleRepo.BatchUpdateStatus(ctx, tenantID, userID, req.IDs, "pending")
		if err != nil {
			response.Success = false
			response.Failed = len(req.IDs)
			response.Errors = append(response.Errors, err.Error())
		} else {
			response.Processed = len(req.IDs)
		}
	case "mark_duplicate":
		err := s.crawlArticleRepo.BatchUpdateStatus(ctx, tenantID, userID, req.IDs, "duplicate")
		if err != nil {
			response.Success = false
			response.Failed = len(req.IDs)
			response.Errors = append(response.Errors, err.Error())
		} else {
			response.Processed = len(req.IDs)
		}
	case "delete":
		err := s.crawlArticleRepo.BatchDelete(ctx, tenantID, userID, req.IDs)
		if err != nil {
			response.Success = false
			response.Failed = len(req.IDs)
			response.Errors = append(response.Errors, err.Error())
		} else {
			response.Processed = len(req.IDs)
		}
	default:
		response.Success = false
		response.Failed = len(req.IDs)
		response.Errors = append(response.Errors, fmt.Sprintf("unknown action: %s", req.Action))
	}

	if response.Success {
		response.Message = fmt.Sprintf("Bulk operation completed successfully for %d items", response.Processed)
	} else {
		response.Message = fmt.Sprintf("Bulk operation failed: %s", strings.Join(response.Errors, ", "))
	}

	return response, nil
}

// ProcessArticle processes a crawl article (extract summary, tags, etc.)
func (s *CrawlArticleService) ProcessArticle(ctx context.Context, tenantID, userID, id uint, req request.ProcessArticleRequest) error {
	fmt.Printf("Processing crawl article %d for tenant %d, user %d\n", id, tenantID, userID)

	// Get existing crawl article
	article, err := s.crawlArticleRepo.GetByID(ctx, tenantID, userID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl article: %w", err)
	}

	if article == nil {
		return fmt.Errorf("crawl article not found")
	}

	// TODO: Implement actual processing logic
	// This would involve:
	// - Extracting summary from content
	// - Extracting tags using NLP
	// - Extracting categories
	// - Translation if requested

	// For now, just update status to processed
	err = s.crawlArticleRepo.UpdateStatus(ctx, tenantID, userID, id, "processed")
	if err != nil {
		fmt.Printf("Failed to update article status after processing: %v\n", err)
		return fmt.Errorf("failed to update article status: %w", err)
	}

	fmt.Printf("Crawl article processed successfully: %d\n", id)
	return nil
}

// CleanupOldArticles deletes articles older than the specified duration
func (s *CrawlArticleService) CleanupOldArticles(ctx context.Context, tenantID, userID uint, olderThan time.Duration) (int64, error) {
	fmt.Printf("Cleaning up old articles for tenant %d, user %d (older than %v)\n", tenantID, userID, olderThan)

	deleted, err := s.crawlArticleRepo.CleanupOldArticles(ctx, tenantID, userID, olderThan)
	if err != nil {
		fmt.Printf("Failed to cleanup old articles: %v\n", err)
		return 0, fmt.Errorf("failed to cleanup old articles: %w", err)
	}

	fmt.Printf("Old articles cleaned up: %d\n", deleted)
	return deleted, nil
}

// CleanupFailedArticles deletes failed articles older than the specified duration
func (s *CrawlArticleService) CleanupFailedArticles(ctx context.Context, tenantID, userID uint, olderThan time.Duration) (int64, error) {
	fmt.Printf("Cleaning up failed articles for tenant %d, user %d (older than %v)\n", tenantID, userID, olderThan)

	deleted, err := s.crawlArticleRepo.CleanupFailedArticles(ctx, tenantID, userID, olderThan)
	if err != nil {
		fmt.Printf("Failed to cleanup failed articles: %v\n", err)
		return 0, fmt.Errorf("failed to cleanup failed articles: %w", err)
	}

	fmt.Printf("Failed articles cleaned up: %d\n", deleted)
	return deleted, nil
}

// Helper methods

// calculateWordCount calculates the word count of content
func (s *CrawlArticleService) calculateWordCount(content string) int {
	// Remove HTML tags and extra whitespace
	cleanContent := utils.StripHTMLTags(content)
	words := strings.Fields(cleanContent)
	return len(words)
}

// generateContentHash generates a hash of the content for duplicate detection
func (s *CrawlArticleService) generateContentHash(content string) string {
	// Remove HTML tags and normalize whitespace
	cleanContent := utils.StripHTMLTags(content)
	cleanContent = strings.TrimSpace(strings.ReplaceAll(cleanContent, "\n", " "))
	
	// Generate MD5 hash
	hash := md5.Sum([]byte(cleanContent))
	return fmt.Sprintf("%x", hash)
}