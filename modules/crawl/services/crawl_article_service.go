package services

import (
	"context"
	"crypto/md5"
	"fmt"
	"strings"
	"time"

	"go.uber.org/zap"

	"wnapi/modules/crawl/dto/request"
	"wnapi/modules/crawl/dto/response"
	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/models"
	"wnapi/modules/crawl/repository"
	"wnapi/pkg/utils"
)

// CrawlArticleService handles crawl article business logic
type CrawlArticleService struct {
	crawlArticleRepo repository.CrawlArticleRepository
	logger           *zap.Logger
}

// NewCrawlArticleService creates a new crawl article service
func NewCrawlArticleService(
	crawlArticleRepo repository.CrawlArticleRepository,
	logger *zap.Logger,
) *CrawlArticleService {
	return &CrawlArticleService{
		crawlArticleRepo: crawlArticleRepo,
		logger:           logger,
	}
}

// Create creates a new crawl article
func (s *CrawlArticleService) Create(ctx context.Context, tenantID uint, req request.CreateCrawlArticleRequest) (*models.CrawlArticle, error) {
	s.logger.Info("Creating crawl article", zap.Uint("tenant_id", tenantID), zap.String("title", req.Title))

	// Generate slug if not provided
	slug := req.Slug
	if slug == "" {
		slug = utils.GenerateSlug(req.Title)
	}

	// Calculate word count
	wordCount := s.calculateWordCount(req.Content)

	// Generate content hash for duplicate detection
	hashContent := s.generateContentHash(req.Content)

	// Check for duplicates
	existingArticle, err := s.crawlArticleRepo.GetByHashContent(ctx, tenantID, hashContent)
	if err != nil {
		s.logger.Error("Failed to check for duplicate content", zap.Error(err))
		return nil, fmt.Errorf("failed to check for duplicate content: %w", err)
	}

	if existingArticle != nil {
		s.logger.Warn("Duplicate content detected", zap.String("url", req.URL), zap.Uint("existing_id", existingArticle.ID))
		return nil, fmt.Errorf("duplicate content detected")
	}

	// Create crawl article model
	article := &models.CrawlArticle{
		TenantID:     tenantID,
		WebsiteID:    req.WebsiteID,
		CrawlJobID:   req.CrawlJobID,
		Title:        req.Title,
		Slug:         slug,
		Content:      req.Content,
		Summary:      req.Summary,
		URL:          req.URL,
		ImageURL:     req.ImageURL,
		Author:       req.Author,
		PublishedAt:  req.PublishedAt,
		Status:       internal.CrawlArticleStatusPending,
		Metadata:     models.ArticleMetadataJSON(req.Metadata),
		Tags:         models.ArticleTagsJSON(req.Tags),
		Categories:   models.ArticleCategoriesJSON(req.Categories),
		WordCount:    wordCount,
		Language:     req.Language,
		HashContent:  hashContent,
		RetryCount:   0,
	}

	// Set default language if not provided
	if article.Language == "" {
		article.Language = "en"
	}

	// Create in database
	if err := s.crawlArticleRepo.Create(ctx, article); err != nil {
		s.logger.Error("Failed to create crawl article", zap.Error(err))
		return nil, fmt.Errorf("failed to create crawl article: %w", err)
	}

	s.logger.Info("Crawl article created successfully", zap.Uint("id", article.ID))
	return article, nil
}

// GetByID gets a crawl article by ID
func (s *CrawlArticleService) GetByID(ctx context.Context, tenantID, id uint) (*response.CrawlArticleResponse, error) {
	article, err := s.crawlArticleRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		s.logger.Error("Failed to get crawl article", zap.Error(err), zap.Uint("id", id))
		return nil, fmt.Errorf("failed to get crawl article: %w", err)
	}

	if article == nil {
		return nil, nil
	}

	return &response.CrawlArticleResponse{
		CrawlArticle: article,
		ReadingTime:  article.GetReadingTime(),
	}, nil
}

// Update updates a crawl article
func (s *CrawlArticleService) Update(ctx context.Context, tenantID, id uint, req request.UpdateCrawlArticleRequest) (*models.CrawlArticle, error) {
	s.logger.Info("Updating crawl article", zap.Uint("tenant_id", tenantID), zap.Uint("id", id))

	// Get existing crawl article
	article, err := s.crawlArticleRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return nil, fmt.Errorf("failed to get crawl article: %w", err)
	}

	if article == nil {
		return nil, fmt.Errorf("crawl article not found")
	}

	// Update fields
	if req.Title != nil {
		article.Title = *req.Title
		// Regenerate slug if title changed
		if req.Slug == nil {
			article.Slug = utils.GenerateSlug(*req.Title)
		}
	}
	if req.Slug != nil {
		article.Slug = *req.Slug
	}
	if req.Content != nil {
		article.Content = *req.Content
		// Recalculate word count and hash
		article.WordCount = s.calculateWordCount(*req.Content)
		article.HashContent = s.generateContentHash(*req.Content)
	}
	if req.Summary != nil {
		article.Summary = *req.Summary
	}
	if req.ImageURL != nil {
		article.ImageURL = *req.ImageURL
	}
	if req.Author != nil {
		article.Author = *req.Author
	}
	if req.PublishedAt != nil {
		article.PublishedAt = req.PublishedAt
	}
	if req.Metadata != nil {
		article.Metadata = models.ArticleMetadataJSON(*req.Metadata)
	}
	if req.Tags != nil {
		article.Tags = models.ArticleTagsJSON(req.Tags)
	}
	if req.Categories != nil {
		article.Categories = models.ArticleCategoriesJSON(req.Categories)
	}
	if req.Language != nil {
		article.Language = *req.Language
	}

	// Update in database
	if err := s.crawlArticleRepo.Update(ctx, article); err != nil {
		s.logger.Error("Failed to update crawl article", zap.Error(err))
		return nil, fmt.Errorf("failed to update crawl article: %w", err)
	}

	s.logger.Info("Crawl article updated successfully", zap.Uint("id", article.ID))
	return article, nil
}

// Delete deletes a crawl article
func (s *CrawlArticleService) Delete(ctx context.Context, tenantID, id uint) error {
	s.logger.Info("Deleting crawl article", zap.Uint("tenant_id", tenantID), zap.Uint("id", id))

	// Delete from database
	if err := s.crawlArticleRepo.Delete(ctx, tenantID, id); err != nil {
		s.logger.Error("Failed to delete crawl article", zap.Error(err))
		return fmt.Errorf("failed to delete crawl article: %w", err)
	}

	s.logger.Info("Crawl article deleted successfully", zap.Uint("id", id))
	return nil
}

// List lists crawl articles with pagination
func (s *CrawlArticleService) List(ctx context.Context, tenantID, websiteID uint, req request.ListCrawlArticleRequest) (*response.CrawlArticleListResponse, error) {
	return s.crawlArticleRepo.List(ctx, tenantID, websiteID, req)
}

// Search searches crawl articles with advanced filters
func (s *CrawlArticleService) Search(ctx context.Context, tenantID uint, req request.SearchCrawlArticleRequest) (*response.CrawlArticleListResponse, error) {
	return s.crawlArticleRepo.Search(ctx, tenantID, req)
}

// UpdateStatus updates the status of a crawl article
func (s *CrawlArticleService) UpdateStatus(ctx context.Context, tenantID, id uint, req request.UpdateCrawlArticleStatusRequest) error {
	s.logger.Info("Updating crawl article status", 
		zap.Uint("tenant_id", tenantID), 
		zap.Uint("id", id), 
		zap.String("status", string(req.Status)))

	return s.crawlArticleRepo.UpdateStatus(ctx, tenantID, id, string(req.Status))
}

// GetStats gets statistics for crawl articles
func (s *CrawlArticleService) GetStats(ctx context.Context, tenantID, websiteID uint) (*response.CrawlArticleStatsResponse, error) {
	return s.crawlArticleRepo.GetStats(ctx, tenantID, websiteID)
}

// GetByCrawlJobID gets crawl articles by crawl job ID
func (s *CrawlArticleService) GetByCrawlJobID(ctx context.Context, tenantID, crawlJobID uint) ([]*models.CrawlArticle, error) {
	return s.crawlArticleRepo.GetByCrawlJobID(ctx, tenantID, crawlJobID)
}

// CheckDuplicate checks if an article is a duplicate
func (s *CrawlArticleService) CheckDuplicate(ctx context.Context, tenantID uint, req request.DuplicateCheckRequest) (*response.DuplicateCheckResponse, error) {
	s.logger.Info("Checking for duplicate article", zap.Uint("tenant_id", tenantID))

	response := &response.DuplicateCheckResponse{
		IsDuplicate: false,
		Similarity:  0.0,
	}

	// Check by URL if provided
	if req.URL != "" {
		existingArticle, err := s.crawlArticleRepo.GetByURL(ctx, tenantID, req.URL)
		if err != nil {
			return nil, fmt.Errorf("failed to check URL duplicate: %w", err)
		}
		if existingArticle != nil {
			response.IsDuplicate = true
			response.Similarity = 1.0
			response.MatchedArticle = existingArticle
			response.Message = "Exact URL match found"
			return response, nil
		}
	}

	// Check by content hash if provided
	if req.HashContent != "" {
		existingArticle, err := s.crawlArticleRepo.GetByHashContent(ctx, tenantID, req.HashContent)
		if err != nil {
			return nil, fmt.Errorf("failed to check content hash duplicate: %w", err)
		}
		if existingArticle != nil {
			response.IsDuplicate = true
			response.Similarity = 1.0
			response.MatchedArticle = existingArticle
			response.Message = "Exact content match found"
			return response, nil
		}
	}

	// Check by content similarity if content is provided
	if req.Content != "" {
		hashContent := s.generateContentHash(req.Content)
		existingArticle, err := s.crawlArticleRepo.GetByHashContent(ctx, tenantID, hashContent)
		if err != nil {
			return nil, fmt.Errorf("failed to check content duplicate: %w", err)
		}
		if existingArticle != nil {
			response.IsDuplicate = true
			response.Similarity = 1.0
			response.MatchedArticle = existingArticle
			response.Message = "Content hash match found"
			return response, nil
		}
	}

	// TODO: Implement fuzzy matching for title similarity
	// This would involve more complex algorithms like Levenshtein distance

	response.Message = "No duplicates found"
	return response, nil
}

// BulkUpdate performs bulk operations on crawl articles
func (s *CrawlArticleService) BulkUpdate(ctx context.Context, tenantID uint, req request.BulkUpdateCrawlArticleRequest) (*response.BulkOperationResponse, error) {
	s.logger.Info("Performing bulk operation on crawl articles", 
		zap.Uint("tenant_id", tenantID), 
		zap.String("action", req.Action), 
		zap.Int("count", len(req.IDs)))

	response := &response.BulkOperationResponse{
		Success:   true,
		Processed: 0,
		Failed:    0,
		Errors:    []string{},
	}

	switch req.Action {
	case "process":
		err := s.crawlArticleRepo.BatchUpdateStatus(ctx, tenantID, req.IDs, string(internal.CrawlArticleStatusProcessed))
		if err != nil {
			response.Success = false
			response.Failed = len(req.IDs)
			response.Errors = append(response.Errors, err.Error())
		} else {
			response.Processed = len(req.IDs)
		}
	case "retry":
		err := s.crawlArticleRepo.BatchUpdateStatus(ctx, tenantID, req.IDs, string(internal.CrawlArticleStatusPending))
		if err != nil {
			response.Success = false
			response.Failed = len(req.IDs)
			response.Errors = append(response.Errors, err.Error())
		} else {
			response.Processed = len(req.IDs)
		}
	case "mark_duplicate":
		err := s.crawlArticleRepo.BatchUpdateStatus(ctx, tenantID, req.IDs, string(internal.CrawlArticleStatusDuplicate))
		if err != nil {
			response.Success = false
			response.Failed = len(req.IDs)
			response.Errors = append(response.Errors, err.Error())
		} else {
			response.Processed = len(req.IDs)
		}
	case "delete":
		err := s.crawlArticleRepo.BatchDelete(ctx, tenantID, req.IDs)
		if err != nil {
			response.Success = false
			response.Failed = len(req.IDs)
			response.Errors = append(response.Errors, err.Error())
		} else {
			response.Processed = len(req.IDs)
		}
	default:
		response.Success = false
		response.Failed = len(req.IDs)
		response.Errors = append(response.Errors, fmt.Sprintf("unknown action: %s", req.Action))
	}

	if response.Success {
		response.Message = fmt.Sprintf("Bulk operation completed successfully for %d items", response.Processed)
	} else {
		response.Message = fmt.Sprintf("Bulk operation failed: %s", strings.Join(response.Errors, ", "))
	}

	return response, nil
}

// ProcessArticle processes a crawl article (extract summary, tags, etc.)
func (s *CrawlArticleService) ProcessArticle(ctx context.Context, tenantID, id uint, req request.ProcessArticleRequest) error {
	s.logger.Info("Processing crawl article", zap.Uint("tenant_id", tenantID), zap.Uint("id", id))

	// Get existing crawl article
	article, err := s.crawlArticleRepo.GetByID(ctx, tenantID, id)
	if err != nil {
		return fmt.Errorf("failed to get crawl article: %w", err)
	}

	if article == nil {
		return fmt.Errorf("crawl article not found")
	}

	// TODO: Implement actual processing logic
	// This would involve:
	// - Extracting summary from content
	// - Extracting tags using NLP
	// - Extracting categories
	// - Translation if requested

	// For now, just update status to processed
	err = s.crawlArticleRepo.UpdateStatus(ctx, tenantID, id, string(internal.CrawlArticleStatusProcessed))
	if err != nil {
		s.logger.Error("Failed to update article status after processing", zap.Error(err))
		return fmt.Errorf("failed to update article status: %w", err)
	}

	s.logger.Info("Crawl article processed successfully", zap.Uint("id", id))
	return nil
}

// CleanupOldArticles deletes articles older than the specified duration
func (s *CrawlArticleService) CleanupOldArticles(ctx context.Context, tenantID uint, olderThan time.Duration) (int64, error) {
	s.logger.Info("Cleaning up old articles", zap.Uint("tenant_id", tenantID), zap.Duration("older_than", olderThan))

	deleted, err := s.crawlArticleRepo.CleanupOldArticles(ctx, tenantID, olderThan)
	if err != nil {
		s.logger.Error("Failed to cleanup old articles", zap.Error(err))
		return 0, fmt.Errorf("failed to cleanup old articles: %w", err)
	}

	s.logger.Info("Old articles cleaned up", zap.Int64("deleted_count", deleted))
	return deleted, nil
}

// CleanupFailedArticles deletes failed articles older than the specified duration
func (s *CrawlArticleService) CleanupFailedArticles(ctx context.Context, tenantID uint, olderThan time.Duration) (int64, error) {
	s.logger.Info("Cleaning up failed articles", zap.Uint("tenant_id", tenantID), zap.Duration("older_than", olderThan))

	deleted, err := s.crawlArticleRepo.CleanupFailedArticles(ctx, tenantID, olderThan)
	if err != nil {
		s.logger.Error("Failed to cleanup failed articles", zap.Error(err))
		return 0, fmt.Errorf("failed to cleanup failed articles: %w", err)
	}

	s.logger.Info("Failed articles cleaned up", zap.Int64("deleted_count", deleted))
	return deleted, nil
}

// Helper methods

// calculateWordCount calculates the word count of content
func (s *CrawlArticleService) calculateWordCount(content string) int {
	// Remove HTML tags and extra whitespace
	cleanContent := utils.StripHTMLTags(content)
	words := strings.Fields(cleanContent)
	return len(words)
}

// generateContentHash generates a hash of the content for duplicate detection
func (s *CrawlArticleService) generateContentHash(content string) string {
	// Remove HTML tags and normalize whitespace
	cleanContent := utils.StripHTMLTags(content)
	cleanContent = strings.TrimSpace(strings.ReplaceAll(cleanContent, "\n", " "))
	
	// Generate MD5 hash
	hash := md5.Sum([]byte(cleanContent))
	return fmt.Sprintf("%x", hash)
}