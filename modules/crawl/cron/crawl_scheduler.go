package cron

import (
	"context"
	"fmt"
	"time"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"

	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/repository"
	"wnapi/modules/crawl/services"
)

// CrawlScheduler handles scheduled crawl jobs
type CrawlScheduler struct {
	cron             *cron.Cron
	crawlJobRepo     repository.CrawlJobRepository
	crawlService     *services.CrawlService
	logger           *zap.Logger
	runningJobs      map[uint]context.CancelFunc // Track running jobs
	scheduledEntries map[uint]cron.EntryID       // Track scheduled entries
}

// NewCrawlScheduler creates a new crawl scheduler
func NewCrawlScheduler(
	crawlJobRepo repository.CrawlJobRepository,
	crawlService *services.CrawlService,
	logger *zap.Logger,
) *CrawlScheduler {
	c := cron.New(cron.WithOptions(
		cron.WithLogger(cron.VerbosePrintfLogger(logger.Sugar())),
		cron.<PERSON><PERSON><PERSON><PERSON>(
			cron.Recover(cron.DefaultLogger),
			cron.DelayIfStillRunning(cron.DefaultLogger),
		),
	))

	return &CrawlScheduler{
		cron:             c,
		crawlJobRepo:     crawlJobRepo,
		crawlService:     crawlService,
		logger:           logger,
		runningJobs:      make(map[uint]context.CancelFunc),
		scheduledEntries: make(map[uint]cron.EntryID),
	}
}

// Start starts the cron scheduler
func (s *CrawlScheduler) Start() error {
	s.logger.Info("Starting crawl scheduler")

	// Load existing scheduled jobs
	err := s.loadScheduledJobs()
	if err != nil {
		return fmt.Errorf("failed to load scheduled jobs: %w", err)
	}

	// Start the cron scheduler
	s.cron.Start()

	// Add a periodic job to check for new scheduled jobs
	_, err = s.cron.AddFunc("@every 5m", s.checkForNewScheduledJobs)
	if err != nil {
		return fmt.Errorf("failed to add periodic job checker: %w", err)
	}

	s.logger.Info("Crawl scheduler started successfully")
	return nil
}

// Stop stops the cron scheduler
func (s *CrawlScheduler) Stop() {
	s.logger.Info("Stopping crawl scheduler")

	// Stop all running jobs
	for jobID, cancel := range s.runningJobs {
		s.logger.Info("Canceling running job", zap.Uint("job_id", jobID))
		cancel()
	}

	// Stop the cron scheduler
	ctx := s.cron.Stop()
	<-ctx.Done()

	s.logger.Info("Crawl scheduler stopped")
}

// AddScheduledJob adds a new scheduled job
func (s *CrawlScheduler) AddScheduledJob(jobID uint, schedule string) error {
	s.logger.Info("Adding scheduled job", zap.Uint("job_id", jobID), zap.String("schedule", schedule))

	// Remove existing entry if any
	if entryID, exists := s.scheduledEntries[jobID]; exists {
		s.cron.Remove(entryID)
		delete(s.scheduledEntries, jobID)
	}

	// Add new scheduled entry
	entryID, err := s.cron.AddFunc(schedule, func() {
		s.executeCrawlJob(jobID)
	})
	if err != nil {
		return fmt.Errorf("failed to add scheduled job: %w", err)
	}

	s.scheduledEntries[jobID] = entryID
	s.logger.Info("Scheduled job added successfully", zap.Uint("job_id", jobID))
	return nil
}

// RemoveScheduledJob removes a scheduled job
func (s *CrawlScheduler) RemoveScheduledJob(jobID uint) {
	s.logger.Info("Removing scheduled job", zap.Uint("job_id", jobID))

	if entryID, exists := s.scheduledEntries[jobID]; exists {
		s.cron.Remove(entryID)
		delete(s.scheduledEntries, jobID)
		s.logger.Info("Scheduled job removed successfully", zap.Uint("job_id", jobID))
	}
}

// UpdateScheduledJob updates a scheduled job
func (s *CrawlScheduler) UpdateScheduledJob(jobID uint, schedule string) error {
	s.logger.Info("Updating scheduled job", zap.Uint("job_id", jobID), zap.String("schedule", schedule))

	// Remove existing entry
	s.RemoveScheduledJob(jobID)

	// Add new entry
	return s.AddScheduledJob(jobID, schedule)
}

// IsJobRunning checks if a job is currently running
func (s *CrawlScheduler) IsJobRunning(jobID uint) bool {
	_, exists := s.runningJobs[jobID]
	return exists
}

// StopRunningJob stops a running job
func (s *CrawlScheduler) StopRunningJob(jobID uint) {
	if cancel, exists := s.runningJobs[jobID]; exists {
		s.logger.Info("Stopping running job", zap.Uint("job_id", jobID))
		cancel()
		delete(s.runningJobs, jobID)
	}
}

// GetScheduledJobs returns all scheduled jobs
func (s *CrawlScheduler) GetScheduledJobs() map[uint]cron.EntryID {
	return s.scheduledEntries
}

// GetRunningJobs returns all running jobs
func (s *CrawlScheduler) GetRunningJobs() []uint {
	jobs := make([]uint, 0, len(s.runningJobs))
	for jobID := range s.runningJobs {
		jobs = append(jobs, jobID)
	}
	return jobs
}

// loadScheduledJobs loads existing scheduled jobs from database
func (s *CrawlScheduler) loadScheduledJobs() error {
	s.logger.Info("Loading scheduled jobs from database")

	ctx := context.Background()

	// Get all scheduled jobs
	jobs, err := s.crawlJobRepo.GetScheduledJobs(ctx)
	if err != nil {
		return fmt.Errorf("failed to get scheduled jobs: %w", err)
	}

	// Add each job to the scheduler
	for _, job := range jobs {
		if job.Schedule != "" && job.Active {
			err := s.AddScheduledJob(job.ID, job.Schedule)
			if err != nil {
				s.logger.Error("Failed to add scheduled job", 
					zap.Uint("job_id", job.ID), 
					zap.String("schedule", job.Schedule),
					zap.Error(err))
				continue
			}
		}
	}

	s.logger.Info("Loaded scheduled jobs", zap.Int("count", len(s.scheduledEntries)))
	return nil
}

// checkForNewScheduledJobs periodically checks for new scheduled jobs
func (s *CrawlScheduler) checkForNewScheduledJobs() {
	s.logger.Debug("Checking for new scheduled jobs")

	ctx := context.Background()

	// Get all scheduled jobs
	jobs, err := s.crawlJobRepo.GetScheduledJobs(ctx)
	if err != nil {
		s.logger.Error("Failed to get scheduled jobs", zap.Error(err))
		return
	}

	// Check for new or updated jobs
	for _, job := range jobs {
		if job.Schedule == "" || !job.Active {
			// Remove if exists
			s.RemoveScheduledJob(job.ID)
			continue
		}

		// Check if job is already scheduled
		if _, exists := s.scheduledEntries[job.ID]; !exists {
			// Add new scheduled job
			err := s.AddScheduledJob(job.ID, job.Schedule)
			if err != nil {
				s.logger.Error("Failed to add new scheduled job", 
					zap.Uint("job_id", job.ID), 
					zap.Error(err))
			}
		}
	}

	// Remove jobs that are no longer scheduled
	scheduledJobIDs := make(map[uint]bool)
	for _, job := range jobs {
		if job.Schedule != "" && job.Active {
			scheduledJobIDs[job.ID] = true
		}
	}

	for jobID := range s.scheduledEntries {
		if !scheduledJobIDs[jobID] {
			s.RemoveScheduledJob(jobID)
		}
	}
}

// executeCrawlJob executes a crawl job
func (s *CrawlScheduler) executeCrawlJob(jobID uint) {
	s.logger.Info("Executing scheduled crawl job", zap.Uint("job_id", jobID))

	// Check if job is already running
	if s.IsJobRunning(jobID) {
		s.logger.Warn("Job is already running, skipping", zap.Uint("job_id", jobID))
		return
	}

	// Create context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	s.runningJobs[jobID] = cancel

	// Execute job in goroutine
	go func() {
		defer func() {
			// Remove from running jobs
			delete(s.runningJobs, jobID)
			
			// Update next run time
			s.updateNextRunTime(jobID)
		}()

		// Update job status and last run time
		err := s.crawlJobRepo.UpdateStatus(ctx, 0, jobID, string(internal.CrawlJobStatusRunning))
		if err != nil {
			s.logger.Error("Failed to update job status", zap.Uint("job_id", jobID), zap.Error(err))
			return
		}

		now := time.Now()
		err = s.crawlJobRepo.UpdateLastRunAt(ctx, 0, jobID, &now)
		if err != nil {
			s.logger.Error("Failed to update last run time", zap.Uint("job_id", jobID), zap.Error(err))
		}

		// Execute crawl
		result, err := s.crawlService.StartCrawl(ctx, jobID)
		if err != nil {
			s.logger.Error("Crawl job failed", zap.Uint("job_id", jobID), zap.Error(err))
			
			// Update job status to failed
			err = s.crawlJobRepo.UpdateStatus(ctx, 0, jobID, string(internal.CrawlJobStatusFailed))
			if err != nil {
				s.logger.Error("Failed to update job status to failed", zap.Uint("job_id", jobID), zap.Error(err))
			}
			return
		}

		s.logger.Info("Crawl job completed successfully", 
			zap.Uint("job_id", jobID),
			zap.Int("pages_processed", result.PagesProcessed),
			zap.Int("articles_found", result.ArticlesFound),
			zap.Duration("duration", result.Duration))
	}()
}

// updateNextRunTime updates the next run time for a scheduled job
func (s *CrawlScheduler) updateNextRunTime(jobID uint) {
	ctx := context.Background()

	// Get job details
	job, err := s.crawlJobRepo.GetByID(ctx, 0, jobID)
	if err != nil {
		s.logger.Error("Failed to get job for next run time update", zap.Uint("job_id", jobID), zap.Error(err))
		return
	}

	if job == nil || job.Schedule == "" {
		return
	}

	// Parse schedule and calculate next run time
	schedule, err := cron.ParseStandard(job.Schedule)
	if err != nil {
		s.logger.Error("Failed to parse schedule", zap.Uint("job_id", jobID), zap.String("schedule", job.Schedule), zap.Error(err))
		return
	}

	nextRun := schedule.Next(time.Now())
	err = s.crawlJobRepo.UpdateNextRunAt(ctx, 0, jobID, &nextRun)
	if err != nil {
		s.logger.Error("Failed to update next run time", zap.Uint("job_id", jobID), zap.Error(err))
	}
}

// GetStats returns scheduler statistics
func (s *CrawlScheduler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"scheduled_jobs_count": len(s.scheduledEntries),
		"running_jobs_count":   len(s.runningJobs),
		"scheduled_jobs":       s.GetScheduledJobs(),
		"running_jobs":         s.GetRunningJobs(),
	}
}