package cron

import (
	"context"
	"fmt"
	"time"

	"github.com/robfig/cron/v3"

	"wnapi/modules/crawl/internal"
	"wnapi/modules/crawl/repository"
	"wnapi/modules/crawl/services"
)

// CrawlScheduler handles scheduled crawl jobs
type CrawlScheduler struct {
	cron             *cron.Cron
	crawlJobRepo     repository.CrawlJobRepository
	crawlService     *services.CrawlService
	runningJobs      map[uint]context.CancelFunc // Track running jobs
	scheduledEntries map[uint]cron.EntryID       // Track scheduled entries
}

// NewCrawlScheduler creates a new crawl scheduler
func NewCrawlScheduler(
	crawlJobRepo repository.CrawlJobRepository,
	crawlService *services.CrawlService,
) *CrawlScheduler {
	c := cron.New(cron.WithOptions(
		cron.WithChain(
			cron.Recover(cron.DefaultLogger),
			cron.DelayIfStillRunning(cron.DefaultLogger),
		),
	))

	return &CrawlScheduler{
		cron:             c,
		crawlJobRepo:     crawlJobRepo,
		crawlService:     crawlService,
		runningJobs:      make(map[uint]context.CancelFunc),
		scheduledEntries: make(map[uint]cron.EntryID),
	}
}

// Start starts the cron scheduler
func (s *CrawlScheduler) Start() error {
	fmt.Println("Starting crawl scheduler")

	// Load existing scheduled jobs
	err := s.loadScheduledJobs()
	if err != nil {
		return fmt.Errorf("failed to load scheduled jobs: %w", err)
	}

	// Start the cron scheduler
	s.cron.Start()

	// Add a periodic job to check for new scheduled jobs
	_, err = s.cron.AddFunc("@every 5m", s.checkForNewScheduledJobs)
	if err != nil {
		return fmt.Errorf("failed to add periodic job checker: %w", err)
	}

	fmt.Println("Crawl scheduler started successfully")
	return nil
}

// Stop stops the cron scheduler
func (s *CrawlScheduler) Stop() {
	fmt.Println("Stopping crawl scheduler")

	// Stop all running jobs
	for jobID, cancel := range s.runningJobs {
		fmt.Printf("Canceling running job %d\n", jobID)
		cancel()
	}

	// Stop the cron scheduler
	ctx := s.cron.Stop()
	<-ctx.Done()

	fmt.Println("Crawl scheduler stopped")
}

// AddScheduledJob adds a new scheduled job
func (s *CrawlScheduler) AddScheduledJob(jobID uint, schedule string) error {
	fmt.Printf("Adding scheduled job %d with schedule %s\n", jobID, schedule)

	// Remove existing entry if any
	if entryID, exists := s.scheduledEntries[jobID]; exists {
		s.cron.Remove(entryID)
		delete(s.scheduledEntries, jobID)
	}

	// Add new scheduled entry
	entryID, err := s.cron.AddFunc(schedule, func() {
		s.executeCrawlJob(jobID)
	})
	if err != nil {
		return fmt.Errorf("failed to add scheduled job: %w", err)
	}

	s.scheduledEntries[jobID] = entryID
	fmt.Printf("Scheduled job %d added successfully\n", jobID)
	return nil
}

// RemoveScheduledJob removes a scheduled job
func (s *CrawlScheduler) RemoveScheduledJob(jobID uint) {
	fmt.Printf("Removing scheduled job %d\n", jobID)

	if entryID, exists := s.scheduledEntries[jobID]; exists {
		s.cron.Remove(entryID)
		delete(s.scheduledEntries, jobID)
		fmt.Printf("Scheduled job %d removed successfully\n", jobID)
	}
}

// UpdateScheduledJob updates a scheduled job
func (s *CrawlScheduler) UpdateScheduledJob(jobID uint, schedule string) error {
	fmt.Printf("Updating scheduled job %d with schedule %s\n", jobID, schedule)

	// Remove existing entry
	s.RemoveScheduledJob(jobID)

	// Add new entry
	return s.AddScheduledJob(jobID, schedule)
}

// IsJobRunning checks if a job is currently running
func (s *CrawlScheduler) IsJobRunning(jobID uint) bool {
	_, exists := s.runningJobs[jobID]
	return exists
}

// StopRunningJob stops a running job
func (s *CrawlScheduler) StopRunningJob(jobID uint) {
	if cancel, exists := s.runningJobs[jobID]; exists {
		fmt.Printf("Stopping running job %d\n", jobID)
		cancel()
		delete(s.runningJobs, jobID)
	}
}

// GetScheduledJobs returns all scheduled jobs
func (s *CrawlScheduler) GetScheduledJobs() map[uint]cron.EntryID {
	return s.scheduledEntries
}

// GetRunningJobs returns all running jobs
func (s *CrawlScheduler) GetRunningJobs() []uint {
	jobs := make([]uint, 0, len(s.runningJobs))
	for jobID := range s.runningJobs {
		jobs = append(jobs, jobID)
	}
	return jobs
}

// loadScheduledJobs loads existing scheduled jobs from database
func (s *CrawlScheduler) loadScheduledJobs() error {
	fmt.Println("Loading scheduled jobs from database")

	ctx := context.Background()

	// Get all scheduled jobs
	jobs, err := s.crawlJobRepo.GetScheduledJobs(ctx)
	if err != nil {
		return fmt.Errorf("failed to get scheduled jobs: %w", err)
	}

	// Add each job to the scheduler
	for _, job := range jobs {
		if job.Schedule != "" && job.Active {
			err := s.AddScheduledJob(job.ID, job.Schedule)
			if err != nil {
				fmt.Printf("Failed to add scheduled job %d with schedule %s: %v\n", job.ID, job.Schedule, err)
				continue
			}
		}
	}

	fmt.Printf("Loaded %d scheduled jobs\n", len(s.scheduledEntries))
	return nil
}

// checkForNewScheduledJobs periodically checks for new scheduled jobs
func (s *CrawlScheduler) checkForNewScheduledJobs() {
	fmt.Println("Checking for new scheduled jobs")

	ctx := context.Background()

	// Get all scheduled jobs
	jobs, err := s.crawlJobRepo.GetScheduledJobs(ctx)
	if err != nil {
		fmt.Printf("Failed to get scheduled jobs: %v\n", err)
		return
	}

	// Check for new or updated jobs
	for _, job := range jobs {
		if job.Schedule == "" || !job.Active {
			// Remove if exists
			s.RemoveScheduledJob(job.ID)
			continue
		}

		// Check if job is already scheduled
		if _, exists := s.scheduledEntries[job.ID]; !exists {
			// Add new scheduled job
			err := s.AddScheduledJob(job.ID, job.Schedule)
			if err != nil {
				fmt.Printf("Failed to add new scheduled job %d: %v\n", job.ID, err)
			}
		}
	}

	// Remove jobs that are no longer scheduled
	scheduledJobIDs := make(map[uint]bool)
	for _, job := range jobs {
		if job.Schedule != "" && job.Active {
			scheduledJobIDs[job.ID] = true
		}
	}

	for jobID := range s.scheduledEntries {
		if !scheduledJobIDs[jobID] {
			s.RemoveScheduledJob(jobID)
		}
	}
}

// executeCrawlJob executes a crawl job
func (s *CrawlScheduler) executeCrawlJob(jobID uint) {
	fmt.Printf("Executing scheduled crawl job %d\n", jobID)

	// Check if job is already running
	if s.IsJobRunning(jobID) {
		fmt.Printf("Job %d is already running, skipping\n", jobID)
		return
	}

	// Create context with cancellation
	ctx, cancel := context.WithCancel(context.Background())
	s.runningJobs[jobID] = cancel

	// Execute job in goroutine
	go func() {
		defer func() {
			// Remove from running jobs
			delete(s.runningJobs, jobID)
			
			// Update next run time
			s.updateNextRunTime(jobID)
		}()

		// Update job status and last run time
		err := s.crawlJobRepo.UpdateStatus(ctx, 0, 0, jobID, string(internal.CrawlJobStatusRunning))
		if err != nil {
			fmt.Printf("Failed to update job status for job %d: %v\n", jobID, err)
			return
		}

		now := time.Now()
		err = s.crawlJobRepo.UpdateLastRunAt(ctx, 0, 0, jobID, &now)
		if err != nil {
			fmt.Printf("Failed to update last run time for job %d: %v\n", jobID, err)
		}

		// Execute crawl
		result, err := s.crawlService.StartCrawl(ctx, 0, 0, jobID)
		if err != nil {
			fmt.Printf("Crawl job %d failed: %v\n", jobID, err)
			
			// Update job status to failed
			err = s.crawlJobRepo.UpdateStatus(ctx, 0, 0, jobID, string(internal.CrawlJobStatusFailed))
			if err != nil {
				fmt.Printf("Failed to update job status to failed for job %d: %v\n", jobID, err)
			}
			return
		}

		fmt.Printf("Crawl job %d completed successfully - Pages: %d, Articles: %d, Duration: %v\n", 
			jobID, result.PagesProcessed, result.ArticlesFound, result.Duration)
	}()
}

// updateNextRunTime updates the next run time for a scheduled job
func (s *CrawlScheduler) updateNextRunTime(jobID uint) {
	ctx := context.Background()

	// Get job details
	job, err := s.crawlJobRepo.GetByID(ctx, 0, 0, jobID)
	if err != nil {
		fmt.Printf("Failed to get job %d for next run time update: %v\n", jobID, err)
		return
	}

	if job == nil || job.Schedule == "" {
		return
	}

	// Parse schedule and calculate next run time
	schedule, err := cron.ParseStandard(job.Schedule)
	if err != nil {
		fmt.Printf("Failed to parse schedule %s for job %d: %v\n", job.Schedule, jobID, err)
		return
	}

	nextRun := schedule.Next(time.Now())
	err = s.crawlJobRepo.UpdateNextRunAt(ctx, 0, 0, jobID, &nextRun)
	if err != nil {
		fmt.Printf("Failed to update next run time for job %d: %v\n", jobID, err)
	}
}

// GetStats returns scheduler statistics
func (s *CrawlScheduler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"scheduled_jobs_count": len(s.scheduledEntries),
		"running_jobs_count":   len(s.runningJobs),
		"scheduled_jobs":       s.GetScheduledJobs(),
		"running_jobs":         s.GetRunningJobs(),
	}
}