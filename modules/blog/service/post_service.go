package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/dto/response"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository/mysql"

	socketDto "wnapi/modules/integration/socket/dto"
	socketService "wnapi/modules/integration/socket/service"
	seoRequest "wnapi/modules/seo/dto/request"
	seoService "wnapi/modules/seo/service"
)

// PostService defines the interface for post business logic
type PostService interface {
	CreatePost(ctx context.Context, tenantID, websiteID uint, req request.CreatePostRequest) (*response.PostResponse, error)
	GetPost(ctx context.Context, tenantID, websiteID uint, postID uint) (*response.PostResponse, error)
	GetPostBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*response.PostResponse, error)
	UpdatePost(ctx context.Context, tenantID, websiteID uint, postID uint, req request.UpdatePostRequest) (*response.PostResponse, error)
	UpdatePostStatus(ctx context.Context, tenantID, websiteID uint, postID uint, req request.UpdatePostStatusRequest) (*response.PostResponse, error)
	DeletePost(ctx context.Context, tenantID, websiteID uint, postID uint) error
	ListPosts(ctx context.Context, tenantID, websiteID uint, req request.ListPostRequest) (*response.PostListResponse, error)
}

// postService implements the PostService interface
type postService struct {
	postRepo        mysql.PostRepository
	categoryRepo    mysql.CategoryRepository
	tagRepo         mysql.TagRepository
	scheduleService ScheduleService
	seoMetaService  seoService.Service
	socketService   socketService.SocketService
}

// NewPostService creates a new PostService instance
func NewPostService(postRepo mysql.PostRepository, categoryRepo mysql.CategoryRepository, tagRepo mysql.TagRepository, scheduleService ScheduleService, seoMetaService seoService.Service, socketSvc socketService.SocketService) PostService {
	return &postService{
		postRepo:        postRepo,
		categoryRepo:    categoryRepo,
		tagRepo:         tagRepo,
		scheduleService: scheduleService,
		seoMetaService:  seoMetaService,
		socketService:   socketSvc,
	}
}

// CreatePost creates a new post
func (s *postService) CreatePost(ctx context.Context, tenantID, websiteID uint, req request.CreatePostRequest) (*response.PostResponse, error) {
	// Create model from request
	userID := getUserIDFromContext(ctx)

	// Convert []int64 to []uint for CategoryIDs
	categoryIDs := make([]uint, len(req.CategoryIDs))
	for i, id := range req.CategoryIDs {
		categoryIDs[i] = uint(id)
	}

	// Convert []int64 to []uint for TagIDs
	tagIDs := make([]uint, len(req.TagIDs))
	for i, id := range req.TagIDs {
		tagIDs[i] = uint(id)
	}

	// Determine AuthorID: use from request if provided, otherwise use current user
	authorID := userID
	if req.AuthorID != 0 {
		authorID = req.AuthorID
	}

	// Create Post model for repository
	post := &models.Post{
		TenantID:      tenantID,
		Title:         req.Title,
		Slug:          req.Slug,
		Description:   getStringValue(req.Description),
		Content:       getStringValue(req.Content),
		Image:         getStringValue(req.Image),
		Status:        req.Status,
		Visibility:    req.Visibility,
		Password:      req.Password,
		CommentStatus: req.CommentStatus,
		PublishedAt:   req.PublishedAt,
		ScheduleAt:    req.ScheduleAt, // Use schedule_at from request
		AuthorID:      authorID,       // Use determined author ID
		CreatedBy:     userID,         // Always use current user from context
		CategoryIDs:   categoryIDs,
		TagIDs:        tagIDs,
	}

	// Save to repository
	if err := s.postRepo.Create(ctx, post); err != nil {
		return nil, err
	}

	// Lưu SEO meta nếu có
	if req.SeoMeta != nil {
		seoReq := toUpsertSeoMetaRequestFromBlog(tenantID, post.PostID, req.SeoMeta)
		_, _ = s.seoMetaService.CreateOrUpdateSeoMeta(ctx, seoReq) // Bỏ qua lỗi để không ảnh hưởng tạo post
	}

	// Handle scheduling if publish date is in the future
	if err := s.handlePostScheduling(ctx, tenantID, websiteID, post, getUserIDFromContext(ctx)); err != nil {
		// Log error but don't fail the post creation
		fmt.Printf("Warning: Failed to create schedule for post %d: %v\n", post.PostID, err)
	}

	// Get the saved post to return with all fields populated
	return s.getPostWithRelations(ctx, tenantID, websiteID, post.PostID, true, true, true)
}

// GetPost retrieves a post by ID
func (s *postService) GetPost(ctx context.Context, tenantID, websiteID uint, postID uint) (*response.PostResponse, error) {
	return s.getPostWithRelations(ctx, tenantID, websiteID, postID, true, true, true)
}

// GetPostBySlug retrieves a post by slug
func (s *postService) GetPostBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*response.PostResponse, error) {
	// Get from repository
	post, err := s.postRepo.GetBySlug(ctx, tenantID, websiteID, slug)
	if err != nil {
		return nil, err
	}

	return s.getPostWithRelations(ctx, tenantID, websiteID, post.PostID, true, true, true)
}

// UpdatePost updates an existing post
func (s *postService) UpdatePost(ctx context.Context, tenantID, websiteID uint, postID uint, req request.UpdatePostRequest) (*response.PostResponse, error) {
	// Get existing post
	existingPost, err := s.postRepo.GetByID(ctx, tenantID, websiteID, postID)
	if err != nil {
		return nil, err
	}

	// Update fields if provided in request
	if req.Title != nil && *req.Title != "" {
		existingPost.Title = *req.Title
	}

	if req.Slug != nil && *req.Slug != "" {
		existingPost.Slug = *req.Slug
	}

	if req.Description != nil {
		existingPost.Description = *req.Description
	}

	if req.Content != nil {
		existingPost.Content = *req.Content
	}

	if req.Image != nil {
		existingPost.Image = getStringValue(req.Image)
	}

	if req.Status != nil && *req.Status != "" {
		existingPost.Status = *req.Status
	}

	if req.Visibility != nil && *req.Visibility != "" {
		existingPost.Visibility = *req.Visibility
	}

	if req.Password != nil {
		existingPost.Password = req.Password
	}

	if req.CommentStatus != nil && *req.CommentStatus != "" {
		existingPost.CommentStatus = *req.CommentStatus
	}

	if req.PublishedAt != nil {
		existingPost.PublishedAt = req.PublishedAt
	}

	if req.ScheduleAt != nil {
		existingPost.ScheduleAt = req.ScheduleAt
	}

	// Update categories and tags if provided
	if req.CategoryIDs != nil {
		// Convert []int64 to []uint for CategoryIDs
		categoryIDs := make([]uint, len(req.CategoryIDs))
		for i, id := range req.CategoryIDs {
			categoryIDs[i] = uint(id)
		}
		existingPost.CategoryIDs = categoryIDs
	}

	if req.TagIDs != nil {
		// Convert []int64 to []uint for TagIDs
		tagIDs := make([]uint, len(req.TagIDs))
		for i, id := range req.TagIDs {
			tagIDs[i] = uint(id)
		}
		existingPost.TagIDs = tagIDs
	}

	// Set editor ID
	//existingPost.CreatedBy = new(uint)
	//*existingPost.CreatedBy = getUserIDFromContext(ctx)

	// Save changes
	if err := s.postRepo.Update(ctx, existingPost); err != nil {
		return nil, err
	}

	// Lưu SEO meta nếu có
	if req.SeoMeta != nil {
		seoReq := toUpsertSeoMetaRequestFromBlog(tenantID, postID, req.SeoMeta)
		_, _ = s.seoMetaService.CreateOrUpdateSeoMeta(ctx, seoReq)
	}

	// Handle scheduling if schedule date or publish date was updated
	if req.ScheduleAt != nil || req.PublishedAt != nil || (req.Status != nil && *req.Status == "schedule") {
		if err := s.handlePostScheduling(ctx, tenantID, websiteID, existingPost, getUserIDFromContext(ctx)); err != nil {
			// Log error but don't fail the post update
			fmt.Printf("Warning: Failed to update schedule for post %d: %v\n", existingPost.PostID, err)
		}
	}

	// Get updated post with related entities
	return s.getPostWithRelations(ctx, tenantID, websiteID, postID, true, true, true)
}

// DeletePost deletes a post
func (s *postService) DeletePost(ctx context.Context, tenantID, websiteID uint, postID uint) error {
	return s.postRepo.Delete(ctx, tenantID, websiteID, postID)
}

// ListPosts lists all posts with pagination
func (s *postService) ListPosts(ctx context.Context, tenantID, websiteID uint, req request.ListPostRequest) (*response.PostListResponse, error) {
	// Get posts from repository
	posts, nextCursor, hasMore, err := s.postRepo.List(ctx, tenantID, websiteID, req)
	if err != nil {
		return nil, err
	}

	// Convert to response
	postResponses := make([]response.PostResponse, 0, len(posts))

	for _, post := range posts {
		// Load relations if requested
		postResp, err := s.enrichPostWithRelations(ctx, tenantID, websiteID, post, req.WithCategories, req.WithTags, req.WithAuthor)
		if err != nil {
			return nil, err
		}
		postResponses = append(postResponses, *postResp)
	}

	return &response.PostListResponse{
		Data: postResponses,
		Meta: struct {
			NextCursor string `json:"next_cursor"`
			HasMore    bool   `json:"has_more"`
		}{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}, nil
}

// Helper functions

// getPostWithRelations fetches a post by ID and loads related entities
func (s *postService) getPostWithRelations(ctx context.Context, tenantID, websiteID, postID uint, withCategories, withTags, withAuthor bool) (*response.PostResponse, error) {
	// Get post from repository
	post, err := s.postRepo.GetByID(ctx, tenantID, websiteID, postID)
	if err != nil {
		return nil, err
	}

	return s.enrichPostWithRelations(ctx, tenantID, websiteID, post, withCategories, withTags, withAuthor)
}

// enrichPostWithRelations loads related entities for a post
func (s *postService) enrichPostWithRelations(ctx context.Context, tenantID, websiteID uint, post *models.Post, withCategories, withTags, withAuthor bool) (*response.PostResponse, error) {
	// Base post response
	var description, content *string
	if post.Description != "" {
		description = &post.Description
	}
	if post.Content != "" {
		content = &post.Content
	}

	var image *string
	if post.Image != "" {
		image = &post.Image
	}

	postResp := &response.PostResponse{
		PostID:        post.PostID,
		TenantID:      post.TenantID,
		Title:         post.Title,
		Slug:          post.Slug,
		Description:   description,
		Content:       content,
		Image:         image,
		Status:        post.Status,
		Visibility:    post.Visibility,
		CommentStatus: post.CommentStatus,
		PublishedAt:   post.PublishedAt,
		CreatedAt:     post.CreatedAt,
		UpdatedAt:     post.UpdatedAt,
		ScheduleAt:    getTimeValue(post.ScheduleAt), // Use ScheduleAt field from post
		AuthorID:      post.AuthorID,
		CreatedBy:     post.CreatedBy,
		Tags:          []response.PostTagResponse{}, // Khởi tạo mảng trống mặc định
	}

	// Load categories if requested
	if withCategories {
		categoryIDs, err := s.postRepo.GetPostCategories(ctx, tenantID, websiteID, post.PostID)
		if err != nil {
			return nil, err
		}

		if len(categoryIDs) > 0 {
			postResp.Categories = make([]response.CategoryResponse, 0, len(categoryIDs))
			for _, catID := range categoryIDs {
				category, err := s.categoryRepo.GetByID(ctx, tenantID, websiteID, catID)
				if err != nil {
					continue // Skip if category not found
				}

				postResp.Categories = append(postResp.Categories, response.CategoryResponse{
					ID:              category.CategoryID,
					ParentID:        category.ParentID,
					Name:            category.Name,
					Slug:            category.Slug,
					Description:     category.Description,
					FeaturedImage:   category.FeaturedImage,
					Depth:           category.Depth,
					Position:        category.Position,
					IsActive:        category.IsActive,
					IsFeatured:      category.IsFeatured,
					MetaTitle:       category.MetaTitle,
					MetaDescription: category.MetaDescription,
					CreatedAt:       category.CreatedAt,
					UpdatedAt:       category.UpdatedAt,
				})
			}
		}
	}

	// Load tags if requested
	if withTags {
		// DEBUG: In ra giá trị withTags và thông tin của post
		fmt.Printf("DEBUG: withTags=%v, tenantID=%d, postID=%d\n", withTags, tenantID, post.PostID)

		tagIDs, err := s.postRepo.GetPostTags(ctx, tenantID, websiteID, post.PostID)
		if err != nil {
			fmt.Printf("DEBUG: Error getting post tags: %v\n", err)
			return nil, err
		}

		// DEBUG: In ra số lượng tagIDs lấy được
		fmt.Printf("DEBUG: Found %d tags for post ID %d\n", len(tagIDs), post.PostID)

		if len(tagIDs) > 0 {
			postResp.Tags = make([]response.PostTagResponse, 0, len(tagIDs))
			// Lấy thông tin chi tiết của từng tag
			for _, tagID := range tagIDs {
				fmt.Printf("DEBUG: Getting tag details for tagID=%d\n", tagID)
				tag, err := s.tagRepo.GetByID(ctx, tenantID, websiteID, tagID)
				if err != nil {
					fmt.Printf("DEBUG: Error getting tag %d: %v\n", tagID, err)
					continue // Bỏ qua nếu không tìm thấy tag
				}

				postResp.Tags = append(postResp.Tags, response.PostTagResponse{
					ID:   tag.TagID,
					Name: tag.Name,
					Slug: tag.Slug,
				})
			}
			// DEBUG: In ra số lượng tags sau khi xử lý
			fmt.Printf("DEBUG: Added %d tags to response\n", len(postResp.Tags))
		} else {
			// Khởi tạo mảng trống cho tags nếu không có tags
			postResp.Tags = []response.PostTagResponse{}
			fmt.Printf("DEBUG: Initialized empty tags array\n")
		}
	} else {
		// Luôn đảm bảo trả về mảng trống nếu không lấy tags
		postResp.Tags = []response.PostTagResponse{}
		fmt.Printf("DEBUG: withTags=false, initialized empty tags array\n")
	}

	// Load author if requested
	if withAuthor && post.Author != nil {
		postResp.Author = &response.UserInfo{
			ID:        post.Author.UserID,
			Username:  post.Author.Username,
			Email:     post.Author.Email,
			FirstName: post.Author.FirstName,
			LastName:  post.Author.LastName,
			AvatarURL: post.Author.AvatarURL,
		}
	}

	// Load created by user if present
	if post.Editor != nil {
		postResp.CreatedByUser = &response.UserInfo{
			ID:        post.Editor.UserID,
			Username:  post.Editor.Username,
			Email:     post.Editor.Email,
			FirstName: post.Editor.FirstName,
			LastName:  post.Editor.LastName,
			AvatarURL: post.Editor.AvatarURL,
		}
	}

	// // Lấy SEO meta cho bài viết
	// seoMeta, err := s.seoMetaService.GetSeoMetaByTable(ctx, seoRequest.GetSeoMetaByTableRequest{
	// 	TableName: "blog_posts",
	// 	TableID:   post.PostID,
	// })
	// if err == nil && seoMeta != nil {
	// 	postResp.SeoMeta = &response.SeoMetaResponse{
	// 		MetaID:          seoMeta.MetaID,
	// 		MetaTitle:       seoMeta.MetaTitle,
	// 		MetaDescription: seoMeta.MetaDescription,
	// 		Keywords:        seoMeta.Keywords,
	// 		CanonicalUrl:    seoMeta.CanonicalUrl,
	// 		OgTitle:         seoMeta.OgTitle,
	// 		OgDescription:   seoMeta.OgDescription,
	// 		OgImage:         seoMeta.OgImage,

	// 		RobotsIndex:      seoMeta.RobotsIndex,
	// 		RobotsFollow:     seoMeta.RobotsFollow,
	// 		RobotsAdvanced:   seoMeta.RobotsAdvanced,
	// 		SeoScore:         seoMeta.SeoScore,
	// 		ReadabilityScore: seoMeta.ReadabilityScore,
	// 		SchemaData:       stringToMap(seoMeta.SchemaData),
	// 	}
	// }

	return postResp, nil
}

// handlePostScheduling creates or updates a schedule for a post if needed
func (s *postService) handlePostScheduling(ctx context.Context, tenantID, websiteID uint, post *models.Post, userID uint) error {
	// Only handle scheduling if we have a schedule service
	if s.scheduleService == nil {
		return nil
	}

	// Only create schedule if:
	// 1. Post has a schedule date
	// 2. Schedule date is in the future
	// 3. Post status is schedule
	scheduleTime := post.ScheduleAt
	if scheduleTime == nil {
		// Fallback to PublishedAt if ScheduleAt is not set
		scheduleTime = post.PublishedAt
	}

	if scheduleTime == nil {
		return nil
	}

	now := time.Now()
	if !scheduleTime.After(now) {
		return nil // Schedule date is not in the future
	}

	if post.Status != "schedule" {
		return nil // Post status is not schedule
	}

	// Create a schedule for this post
	// Default max retries to 3
	maxRetries := uint8(3)

	_, err := s.scheduleService.CreateSchedule(ctx, tenantID, websiteID, post.PostID, userID, *scheduleTime, maxRetries)
	if err != nil {
		return fmt.Errorf("failed to create schedule: %w", err)
	}

	return nil
}

// Helper functions

// getStringValue safely gets string value from pointer
func getStringValue(s *string) string {
	if s == nil {
		return ""
	}
	return *s
}

// getTimeValue safely gets time value from pointer
func getTimeValue(t *time.Time) time.Time {
	if t == nil {
		return time.Time{}
	}
	return *t
}

// Helper chuyển đổi SeoMetaRequest sang UpsertSeoMetaRequest
func toUpsertSeoMetaRequestFromBlog(tenantID, postID uint, req *request.SeoMetaRequest) seoRequest.UpsertSeoMetaRequest {
	return seoRequest.UpsertSeoMetaRequest{
		TableID:         postID,
		TableName:       "blog_posts",
		MetaTitle:       req.MetaTitle,
		MetaDescription: req.MetaDescription,
		Keywords:        req.Keywords,
		CanonicalUrl:    req.CanonicalUrl,
		OgTitle:         req.OgTitle,
		OgDescription:   req.OgDescription,
		OgImage:         req.OgImage,

		RobotsIndex:      req.RobotsIndex,
		RobotsFollow:     req.RobotsFollow,
		RobotsAdvanced:   req.RobotsAdvanced,
		SeoScore:         req.SeoScore,
		ReadabilityScore: req.ReadabilityScore,
		SchemaData:       mapToString(req.SchemaData),
	}
}

// Helper chuyển đổi map[string]interface{} sang string (json)
func mapToString(m map[string]interface{}) string {
	if m == nil {
		return ""
	}
	b, _ := json.Marshal(m)
	return string(b)
}

// Helper chuyển đổi string json sang map[string]interface{}
func stringToMap(s string) map[string]interface{} {
	if s == "" {
		return nil
	}
	var m map[string]interface{}
	_ = json.Unmarshal([]byte(s), &m)
	return m
}

// sendPostPublishedNotification sends socket notification when post is published
func (s *postService) sendPostPublishedNotification(ctx context.Context, tenantID uint, post *models.Post) {
	if s.socketService == nil {
		return // Socket service not available
	}

	// Create notification request
	notificationReq := socketDto.BroadcastNotificationRequest{
		TenantID:  tenantID,
		WebsiteID: tenantID, // Using tenantID as websiteID for now
		Type:      "blog.post.published",
		Title:     "Bài viết mới được xuất bản",
		Message:   fmt.Sprintf("Bài viết '%s' đã được xuất bản", post.Title),
		Priority:  "normal",
		Category:  "blog",
		Channels:  []string{"user", "notification"},
		Data: map[string]interface{}{
			"post_id":    post.PostID,
			"post_title": post.Title,
			"post_slug":  post.Slug,
			"author_id":  post.AuthorID,
			"tenant_id":  tenantID,
			"timestamp":  time.Now(),
		},
	}

	// Send broadcast notification
	if err := s.socketService.NotificationService().BroadcastNotification(ctx, notificationReq); err != nil {
		fmt.Printf("Warning: Failed to send post published notification for post %d: %v\n", post.PostID, err)
	}
}

// UpdatePostStatus updates only the status of an existing post
func (s *postService) UpdatePostStatus(ctx context.Context, tenantID, websiteID uint, postID uint, req request.UpdatePostStatusRequest) (*response.PostResponse, error) {
	// Get existing post
	existingPost, err := s.postRepo.GetByID(ctx, tenantID, websiteID, postID)
	if err != nil {
		return nil, err
	}

	// Store old status to check for changes
	oldStatus := existingPost.Status

	// Update status
	existingPost.Status = req.Status

	// Save changes
	if err := s.postRepo.Update(ctx, existingPost); err != nil {
		return nil, err
	}

	// Send socket notification if post was published
	if oldStatus != "published" && req.Status == "published" {
		s.sendPostPublishedNotification(ctx, tenantID, existingPost)
	}

	// Handle scheduling if status is 'schedule' and publish date is in the future
	if req.Status == models.STATUS_SCHEDULE && existingPost.PublishedAt != nil {
		if err := s.handlePostScheduling(ctx, tenantID, websiteID, existingPost, getUserIDFromContext(ctx)); err != nil {
			// Log error but don't fail the status update
			fmt.Printf("Warning: Failed to update schedule for post %d: %v\n", existingPost.PostID, err)
		}
	}

	// Get updated post with related entities
	return s.getPostWithRelations(ctx, tenantID, websiteID, postID, true, true, true)
}
