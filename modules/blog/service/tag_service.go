package service

import (
	"context"
	"fmt"
	"strings"

	"github.com/gosimple/slug"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/dto/response"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository/mysql"
)

// TagService defines the interface for tag service operations
type TagService interface {
	Create(ctx context.Context, tenantID, websiteID uint, req request.CreateTagRequest) (*response.TagResponse, error)
	GetByID(ctx context.Context, tenantID, websiteID, tagID uint) (*response.TagResponse, error)
	GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*response.TagResponse, error)
	Update(ctx context.Context, tenantID, websiteID, tagID uint, req request.UpdateTagRequest) (*response.TagResponse, error)
	Delete(ctx context.Context, tenantID, websiteID, tagID uint) error
	List(ctx context.Context, tenantID, websiteID uint, req request.ListTagRequest) ([]response.TagResponse, string, bool, error)
	GetTagsWithPostCount(ctx context.Context, tenantID, websiteID uint) ([]response.TagResponse, error)
	FindOrCreate(ctx context.Context, tenantID, websiteID uint, req request.CreateTagRequest) (*response.TagResponse, error)
}

// tagService implements the TagService interface
type tagService struct {
	tagRepo mysql.TagRepository
}

// NewTagService creates a new instance of TagService
func NewTagService(tagRepo mysql.TagRepository) TagService {
	return &tagService{tagRepo: tagRepo}
}

// Create creates a new tag
func (s *tagService) Create(ctx context.Context, tenantID, websiteID uint, req request.CreateTagRequest) (*response.TagResponse, error) {
	// Create tag model
	tag := &models.Tag{
		TenantID:    tenantID,
		WebsiteID:   websiteID,
		Name:        req.Name,
		Description: req.Description,
	}

	// Set slug if provided, otherwise generate from name
	if req.Slug != "" {
		tag.Slug = req.Slug
	} else {
		tag.Slug = slug.Make(req.Name)
	}

	// Set active status if provided
	if req.IsActive != nil {
		tag.IsActive = *req.IsActive
	} else {
		tag.IsActive = true
	}

	// Create tag in repository
	if err := s.tagRepo.Create(ctx, tag); err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") && strings.Contains(err.Error(), "unique_blog_tag_slug_tenant") {
			return nil, fmt.Errorf("tag with slug '%s' already exists", tag.Slug)
		}
		return nil, fmt.Errorf("failed to create tag: %w", err)
	}

	// Get complete tag with all fields
	createdTag, err := s.tagRepo.GetByID(ctx, tenantID, websiteID, tag.TagID)
	if err != nil {
		return nil, fmt.Errorf("tag created but failed to retrieve: %w", err)
	}

	// Convert to response
	resp := response.FromTag(createdTag)
	return &resp, nil
}

// GetByID gets a tag by ID
func (s *tagService) GetByID(ctx context.Context, tenantID, websiteID, tagID uint) (*response.TagResponse, error) {
	tag, err := s.tagRepo.GetByID(ctx, tenantID, websiteID, tagID)
	if err != nil {
		return nil, err
	}

	resp := response.FromTag(tag)
	return &resp, nil
}

// GetBySlug gets a tag by slug
func (s *tagService) GetBySlug(ctx context.Context, tenantID, websiteID uint, slug string) (*response.TagResponse, error) {
	tag, err := s.tagRepo.GetBySlug(ctx, tenantID, websiteID, slug)
	if err != nil {
		return nil, err
	}

	resp := response.FromTag(tag)
	return &resp, nil
}

// Update updates an existing tag
func (s *tagService) Update(ctx context.Context, tenantID, websiteID, tagID uint, req request.UpdateTagRequest) (*response.TagResponse, error) {
	// Get existing tag
	existingTag, err := s.tagRepo.GetByID(ctx, tenantID, websiteID, tagID)
	if err != nil {
		return nil, err
	}

	// Update fields
	existingTag.Name = req.Name

	// Set slug if provided, otherwise generate from name
	if req.Slug != "" {
		existingTag.Slug = req.Slug
	} else {
		existingTag.Slug = slug.Make(req.Name)
	}

	existingTag.Description = req.Description

	// Set active status if provided
	if req.IsActive != nil {
		existingTag.IsActive = *req.IsActive
	}

	// Update tag in repository
	if err := s.tagRepo.Update(ctx, existingTag); err != nil {
		if strings.Contains(err.Error(), "Duplicate entry") && strings.Contains(err.Error(), "unique_blog_tag_slug_tenant") {
			return nil, fmt.Errorf("tag with slug '%s' already exists", existingTag.Slug)
		}
		return nil, fmt.Errorf("failed to update tag: %w", err)
	}

	// Get updated tag
	updatedTag, err := s.tagRepo.GetByID(ctx, tenantID, websiteID, tagID)
	if err != nil {
		return nil, fmt.Errorf("tag updated but failed to retrieve: %w", err)
	}

	// Convert to response
	resp := response.FromTag(updatedTag)
	return &resp, nil
}

// Delete deletes a tag
func (s *tagService) Delete(ctx context.Context, tenantID, websiteID, tagID uint) error {
	return s.tagRepo.Delete(ctx, tenantID, websiteID, tagID)
}

// List lists tags with pagination
func (s *tagService) List(ctx context.Context, tenantID, websiteID uint, req request.ListTagRequest) ([]response.TagResponse, string, bool, error) {
	tags, nextCursor, hasMore, err := s.tagRepo.List(ctx, tenantID, websiteID, req)
	if err != nil {
		return nil, "", false, err
	}

	// Convert to response
	responses := response.FromTagList(tags)
	return responses, nextCursor, hasMore, nil
}

// GetTagsWithPostCount gets tags with post count
func (s *tagService) GetTagsWithPostCount(ctx context.Context, tenantID, websiteID uint) ([]response.TagResponse, error) {
	tags, err := s.tagRepo.GetTagsWithPostCount(ctx, tenantID, websiteID)
	if err != nil {
		return nil, err
	}

	// Convert to response
	responses := response.FromTagList(tags)
	return responses, nil
}

// FindOrCreate finds a tag by slug or creates a new one if it doesn't exist
func (s *tagService) FindOrCreate(ctx context.Context, tenantID, websiteID uint, req request.CreateTagRequest) (*response.TagResponse, error) {
	// Xác định slug
	tagSlug := req.Slug
	if tagSlug == "" {
		tagSlug = slug.Make(req.Name)
	}

	// Tìm tag theo slug
	existingTag, err := s.tagRepo.GetBySlug(ctx, tenantID, websiteID, tagSlug)
	if err == nil {
		// Nếu tìm thấy, trả về tag đó
		resp := response.FromTag(existingTag)
		return &resp, nil
	}

	// Nếu không tìm thấy hoặc có lỗi khác, tạo tag mới
	return s.Create(ctx, tenantID, websiteID, req)
}
