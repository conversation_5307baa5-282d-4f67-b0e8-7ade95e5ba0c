package service

import (
	"context"
	"fmt"
	"strconv"

	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/dto/response"
	"wnapi/modules/blog/models"
	"wnapi/modules/blog/repository"
	"wnapi/modules/blog/repository/mysql"
)

// RelatedPostService defines the interface for related post business logic
type RelatedPostService interface {
	CreateRelatedPost(ctx context.Context, tenantID, websiteID uint, req request.CreateRelatedPostRequest) (*response.RelatedPostResponse, error)
	GetRelatedPost(ctx context.Context, tenantID, websiteID uint, relationID uint) (*response.RelatedPostResponse, error)
	UpdateRelatedPost(ctx context.Context, tenantID, websiteID uint, relationID uint, req request.UpdateRelatedPostRequest) (*response.RelatedPostResponse, error)
	DeleteRelatedPost(ctx context.Context, tenantID, websiteID uint, relationID uint) error
	DeleteRelatedPostByPosts(ctx context.Context, tenantID, websiteID uint, req request.DeleteRelatedPostRequest) error
	ListRelatedPosts(ctx context.Context, tenantID, websiteID uint, req request.ListRelatedPostRequest) (*response.RelatedPostListResponse, error)
	GetPostRelatedPosts(ctx context.Context, tenantID, websiteID uint, postID uint, limit int) (*response.PostRelatedPostsResponse, error)
	GetPostRelatedPostsWithCursor(ctx context.Context, tenantID, websiteID uint, postID uint, limit int, cursor string) (*response.PostRelatedPostsResponse, error)
	BulkCreateRelatedPosts(ctx context.Context, tenantID, websiteID uint, req request.BulkCreateRelatedPostRequest) (*response.BulkRelatedPostResponse, error)
	DeleteAllRelatedPosts(ctx context.Context, tenantID, websiteID uint, postID uint) error
	UpdatePostRelatedPosts(ctx context.Context, tenantID, websiteID uint, req request.UpdatePostRelatedPostsRequest) (*response.PostRelatedPostsResponse, error)
	DeletePostRelatedPost(ctx context.Context, tenantID, websiteID uint, req request.DeletePostRelatedPostRequest) error
}

// relatedPostService implements the RelatedPostService interface
type relatedPostService struct {
	relatedPostRepo repository.RelatedPostRepository
	postRepo        *mysql.PostRepository
}

// NewRelatedPostService creates a new RelatedPostService instance
func NewRelatedPostService(
	relatedPostRepo repository.RelatedPostRepository,
	postRepo *mysql.PostRepository,
) RelatedPostService {
	return &relatedPostService{
		relatedPostRepo: relatedPostRepo,
		postRepo:        postRepo,
	}
}

// CreateRelatedPost creates a new related post relationship
func (s *relatedPostService) CreateRelatedPost(ctx context.Context, tenantID, websiteID uint, req request.CreateRelatedPostRequest) (*response.RelatedPostResponse, error) {
	// Validate that both posts exist
	if err := s.validatePostsExist(ctx, tenantID, websiteID, req.PostID, req.RelatedPostID); err != nil {
		return nil, err
	}

	// Check if relationship already exists
	exists, err := s.relatedPostRepo.CheckRelationExists(ctx, tenantID, websiteID, req.PostID, req.RelatedPostID)
	if err != nil {
		return nil, fmt.Errorf("failed to check relation exists: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("relationship already exists between posts %d and %d", req.PostID, req.RelatedPostID)
	}

	// Set default values
	priority := req.Priority
	if priority == 0 {
		priority = 1
	}

	isBidirectional := true
	if req.IsBidirectional != nil {
		isBidirectional = *req.IsBidirectional
	}

	// Get user ID from context
	userID := getUserIDFromContext(ctx)

	// Create related post model
	relatedPost := &models.RelatedPost{
		TenantID:        tenantID,
		PostID:          req.PostID,
		RelatedPostID:   req.RelatedPostID,
		Priority:        priority,
		IsBidirectional: isBidirectional,
		CreatedBy:       userID,
	}

	// Save to repository
	if err := s.relatedPostRepo.Create(ctx, relatedPost); err != nil {
		return nil, fmt.Errorf("failed to create related post: %w", err)
	}

	// Create bidirectional relationship if needed
	if isBidirectional {
		// Check if reverse relationship already exists
		reverseExists, err := s.relatedPostRepo.CheckRelationExists(ctx, tenantID, websiteID, req.RelatedPostID, req.PostID)
		if err != nil {
			return nil, fmt.Errorf("failed to check reverse relation exists: %w", err)
		}

		if !reverseExists {
			reverseRelatedPost := &models.RelatedPost{
				TenantID:        tenantID,
				PostID:          req.RelatedPostID,
				RelatedPostID:   req.PostID,
				Priority:        priority,
				IsBidirectional: isBidirectional,
				CreatedBy:       userID,
			}

			if err := s.relatedPostRepo.Create(ctx, reverseRelatedPost); err != nil {
				// Log error but don't fail the main operation
				fmt.Printf("Warning: Failed to create reverse relationship: %v\n", err)
			}
		}
	}

	// Return the created relationship with enriched data
	return s.enrichRelatedPostResponse(ctx, tenantID, websiteID, relatedPost)
}

// GetRelatedPost retrieves a related post relationship by ID
func (s *relatedPostService) GetRelatedPost(ctx context.Context, tenantID, websiteID uint, relationID uint) (*response.RelatedPostResponse, error) {
	relatedPost, err := s.relatedPostRepo.GetByID(ctx, tenantID, websiteID, relationID)
	if err != nil {
		return nil, err
	}

	return s.enrichRelatedPostResponse(ctx, tenantID, websiteID, relatedPost)
}

// UpdateRelatedPost updates a related post relationship
func (s *relatedPostService) UpdateRelatedPost(ctx context.Context, tenantID, websiteID uint, relationID uint, req request.UpdateRelatedPostRequest) (*response.RelatedPostResponse, error) {
	// Get existing relationship
	existingRelatedPost, err := s.relatedPostRepo.GetByID(ctx, tenantID, websiteID, relationID)
	if err != nil {
		return nil, err
	}

	// Update fields if provided
	if req.Priority != nil {
		existingRelatedPost.Priority = *req.Priority
	}
	if req.IsBidirectional != nil {
		existingRelatedPost.IsBidirectional = *req.IsBidirectional
	}

	// Save changes
	if err := s.relatedPostRepo.Update(ctx, existingRelatedPost); err != nil {
		return nil, fmt.Errorf("failed to update related post: %w", err)
	}

	// Return updated relationship with enriched data
	return s.enrichRelatedPostResponse(ctx, tenantID, websiteID, existingRelatedPost)
}

// DeleteRelatedPost deletes a related post relationship by ID
func (s *relatedPostService) DeleteRelatedPost(ctx context.Context, tenantID, websiteID uint, relationID uint) error {
	return s.relatedPostRepo.Delete(ctx, tenantID, websiteID, relationID)
}

// DeleteRelatedPostByPosts deletes a specific relationship between two posts
func (s *relatedPostService) DeleteRelatedPostByPosts(ctx context.Context, tenantID, websiteID uint, req request.DeleteRelatedPostRequest) error {
	return s.relatedPostRepo.DeleteByPosts(ctx, tenantID, websiteID, req.PostID, req.RelatedPostID)
}

// ListRelatedPosts lists related posts with pagination
func (s *relatedPostService) ListRelatedPosts(ctx context.Context, tenantID, websiteID uint, req request.ListRelatedPostRequest) (*response.RelatedPostListResponse, error) {
	// Get related posts from repository
	relatedPosts, nextCursor, hasMore, err := s.relatedPostRepo.List(ctx, tenantID, websiteID, req)
	if err != nil {
		return nil, err
	}

	// Convert to response DTOs
	responseData := make([]response.RelatedPostResponse, 0, len(relatedPosts))
	for _, rp := range relatedPosts {
		enrichedResponse, err := s.enrichRelatedPostResponse(ctx, tenantID, websiteID, rp)
		if err != nil {
			return nil, fmt.Errorf("failed to enrich related post response: %w", err)
		}
		responseData = append(responseData, *enrichedResponse)
	}

	return &response.RelatedPostListResponse{
		Data: responseData,
		Meta: response.RelatedPostListMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
		},
	}, nil
}

// GetPostRelatedPosts retrieves all related posts for a specific post
func (s *relatedPostService) GetPostRelatedPosts(ctx context.Context, tenantID, websiteID uint, postID uint, limit int) (*response.PostRelatedPostsResponse, error) {
	// Validate that post exists
	if err := s.validatePostExists(ctx, tenantID, websiteID, postID); err != nil {
		return nil, err
	}

	// Get related posts with limit + 1 to check if there are more
	relatedPosts, err := s.relatedPostRepo.GetRelatedPosts(ctx, tenantID, websiteID, postID, limit+1)
	if err != nil {
		return nil, err
	}

	// Check if there are more posts
	hasMore := len(relatedPosts) > limit
	if hasMore {
		relatedPosts = relatedPosts[:limit] // Remove the extra post
	}

	// Convert to post summary responses
	postSummaries := make([]response.PostSummaryResponse, 0, len(relatedPosts))
	var nextCursor string

	for _, rp := range relatedPosts {
		// Get related post details
		relatedPostDetails, err := s.postRepo.GetByID(ctx, tenantID, websiteID, rp.RelatedPostID)
		if err != nil {
			continue // Skip if post not found
		}

		postSummary := *s.convertToPostSummary(relatedPostDetails)
		postSummaries = append(postSummaries, postSummary)
	}

	// Generate next cursor if there are more posts
	if hasMore && len(postSummaries) > 0 {
		lastPost := postSummaries[len(postSummaries)-1]
		nextCursor = fmt.Sprintf("%d", lastPost.PostID)
	}

	return &response.PostRelatedPostsResponse{
		Data: postSummaries,
		Meta: response.PostRelatedPostsCursorMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
			Total:      len(postSummaries),
		},
	}, nil
}

// GetPostRelatedPostsWithCursor gets all related posts for a specific post with cursor-based pagination
func (s *relatedPostService) GetPostRelatedPostsWithCursor(ctx context.Context, tenantID, websiteID uint, postID uint, limit int, cursor string) (*response.PostRelatedPostsResponse, error) {
	// Validate that post exists
	if err := s.validatePostExists(ctx, tenantID, websiteID, postID); err != nil {
		return nil, err
	}

	// Parse cursor if provided
	var cursorID uint
	if cursor != "" {
		parsedCursor, err := strconv.ParseUint(cursor, 10, 32)
		if err != nil {
			return nil, fmt.Errorf("invalid cursor format")
		}
		cursorID = uint(parsedCursor)
	}

	// Get related posts with cursor and limit + 1 to check if there are more
	relatedPosts, err := s.relatedPostRepo.GetRelatedPostsWithCursor(ctx, tenantID, websiteID, postID, cursorID, limit+1)
	if err != nil {
		return nil, err
	}

	// Check if there are more posts
	hasMore := len(relatedPosts) > limit
	if hasMore {
		relatedPosts = relatedPosts[:limit] // Remove the extra post
	}

	// Convert to post summary responses
	postSummaries := make([]response.PostSummaryResponse, 0, len(relatedPosts))
	var nextCursor string

	for _, rp := range relatedPosts {
		// Get related post details
		relatedPostDetails, err := s.postRepo.GetByID(ctx, tenantID, websiteID, rp.RelatedPostID)
		if err != nil {
			continue // Skip if post not found
		}

		postSummary := *s.convertToPostSummary(relatedPostDetails)
		postSummaries = append(postSummaries, postSummary)
	}

	// Generate next cursor if there are more posts
	if hasMore && len(postSummaries) > 0 {
		lastPost := postSummaries[len(postSummaries)-1]
		nextCursor = fmt.Sprintf("%d", lastPost.PostID)
	}

	return &response.PostRelatedPostsResponse{
		Data: postSummaries,
		Meta: response.PostRelatedPostsCursorMeta{
			NextCursor: nextCursor,
			HasMore:    hasMore,
			Total:      len(postSummaries),
		},
	}, nil
}

// BulkCreateRelatedPosts creates multiple related post relationships
func (s *relatedPostService) BulkCreateRelatedPosts(ctx context.Context, tenantID, websiteID uint, req request.BulkCreateRelatedPostRequest) (*response.BulkRelatedPostResponse, error) {
	// Validate main post exists
	if err := s.validatePostExists(ctx, tenantID, websiteID, req.PostID); err != nil {
		return nil, err
	}

	userID := getUserIDFromContext(ctx)
	isBidirectional := true
	if req.IsBidirectional != nil {
		isBidirectional = *req.IsBidirectional
	}

	var created []response.RelatedPostResponse
	var failed []response.BulkRelatedPostError

	for _, item := range req.RelatedPosts {
		// Validate related post exists
		if err := s.validatePostExists(ctx, tenantID, websiteID, item.RelatedPostID); err != nil {
			failed = append(failed, response.BulkRelatedPostError{
				RelatedPostID: item.RelatedPostID,
				Error:         err.Error(),
				Code:          "POST_NOT_FOUND",
			})
			continue
		}

		// Check if relationship already exists
		exists, err := s.relatedPostRepo.CheckRelationExists(ctx, tenantID, websiteID, req.PostID, item.RelatedPostID)
		if err != nil {
			failed = append(failed, response.BulkRelatedPostError{
				RelatedPostID: item.RelatedPostID,
				Error:         err.Error(),
				Code:          "CHECK_FAILED",
			})
			continue
		}
		if exists {
			failed = append(failed, response.BulkRelatedPostError{
				RelatedPostID: item.RelatedPostID,
				Error:         "relationship already exists",
				Code:          "ALREADY_EXISTS",
			})
			continue
		}

		// Create relationship
		priority := item.Priority
		if priority == 0 {
			priority = 1
		}

		relatedPost := &models.RelatedPost{
			TenantID:        tenantID,
			PostID:          req.PostID,
			RelatedPostID:   item.RelatedPostID,
			Priority:        priority,
			IsBidirectional: isBidirectional,
			CreatedBy:       userID,
		}

		if err := s.relatedPostRepo.Create(ctx, relatedPost); err != nil {
			failed = append(failed, response.BulkRelatedPostError{
				RelatedPostID: item.RelatedPostID,
				Error:         err.Error(),
				Code:          "CREATE_FAILED",
			})
			continue
		}

		// Enrich and add to created list
		enrichedResponse, err := s.enrichRelatedPostResponse(ctx, tenantID, websiteID, relatedPost)
		if err != nil {
			// Log error but don't fail
			fmt.Printf("Warning: Failed to enrich response for relation %d: %v\n", relatedPost.ID, err)
			continue
		}
		created = append(created, *enrichedResponse)
	}

	return &response.BulkRelatedPostResponse{
		Created: created,
		Failed:  failed,
		Meta: response.BulkRelatedPostMeta{
			TotalRequested: len(req.RelatedPosts),
			TotalCreated:   len(created),
			TotalFailed:    len(failed),
		},
	}, nil
}

// DeleteAllRelatedPosts deletes all related post relationships for a specific post
func (s *relatedPostService) DeleteAllRelatedPosts(ctx context.Context, tenantID, websiteID uint, postID uint) error {
	// Validate that post exists
	if err := s.validatePostExists(ctx, tenantID, websiteID, postID); err != nil {
		return err
	}

	return s.relatedPostRepo.DeleteAllByPost(ctx, tenantID, websiteID, postID)
}

// Helper functions

// enrichRelatedPostResponse enriches a related post with additional data
func (s *relatedPostService) enrichRelatedPostResponse(ctx context.Context, tenantID, websiteID uint, relatedPost *models.RelatedPost) (*response.RelatedPostResponse, error) {
	resp := &response.RelatedPostResponse{
		ID:              relatedPost.ID,
		TenantID:        relatedPost.TenantID,
		PostID:          relatedPost.PostID,
		RelatedPostID:   relatedPost.RelatedPostID,
		Priority:        relatedPost.Priority,
		IsBidirectional: relatedPost.IsBidirectional,
		CreatedAt:       relatedPost.CreatedAt,
		UpdatedAt:       relatedPost.UpdatedAt,
		CreatedBy:       relatedPost.CreatedBy,
	}

	// Optionally load post details
	if post, err := s.postRepo.GetByID(ctx, tenantID, websiteID, relatedPost.PostID); err == nil {
		resp.Post = s.convertToPostSummary(post)
	}

	if relatedPostDetails, err := s.postRepo.GetByID(ctx, tenantID, websiteID, relatedPost.RelatedPostID); err == nil {
		resp.RelatedPost = s.convertToPostSummary(relatedPostDetails)
	}

	return resp, nil
}

// convertToPostSummary converts a post model to a post summary response
func (s *relatedPostService) convertToPostSummary(post *models.Post) *response.PostSummaryResponse {
	return &response.PostSummaryResponse{
		PostID:      post.PostID,
		Title:       post.Title,
		Slug:        post.Slug,
		Description: &post.Description,
		Image:       &post.Image,
		Status:      post.Status,
		Visibility:  post.Visibility,
		PublishedAt: post.PublishedAt,
		CreatedAt:   post.CreatedAt,
		AuthorID:    post.AuthorID,
	}
}

// validatePostExists validates that a post exists
func (s *relatedPostService) validatePostExists(ctx context.Context, tenantID, websiteID uint, postID uint) error {
	_, err := s.postRepo.GetByID(ctx, tenantID, websiteID, postID)
	if err != nil {
		return fmt.Errorf("post %d not found: %w", postID, err)
	}
	return nil
}

// validatePostsExist validates that both posts exist
func (s *relatedPostService) validatePostsExist(ctx context.Context, tenantID, websiteID uint, postID1, postID2 uint) error {
	if err := s.validatePostExists(ctx, tenantID, websiteID, postID1); err != nil {
		return err
	}
	if err := s.validatePostExists(ctx, tenantID, websiteID, postID2); err != nil {
		return err
	}
	if postID1 == postID2 {
		return fmt.Errorf("cannot create relationship between the same post")
	}
	return nil
}

// UpdatePostRelatedPosts cập nhật danh sách bài viết liên quan cho một bài viết
func (s *relatedPostService) UpdatePostRelatedPosts(ctx context.Context, tenantID, websiteID uint, req request.UpdatePostRelatedPostsRequest) (*response.PostRelatedPostsResponse, error) {
	// Validate that post exists
	if err := s.validatePostExists(ctx, tenantID, websiteID, req.PostID); err != nil {
		return nil, err
	}

	// Xóa tất cả các mối quan hệ hiện tại
	if err := s.relatedPostRepo.DeleteAllByPost(ctx, tenantID, websiteID, req.PostID); err != nil {
		return nil, fmt.Errorf("failed to delete existing related posts: %w", err)
	}

	// Lấy user ID từ context
	userID := getUserIDFromContext(ctx)

	// Tạo các mối quan hệ mới
	for _, relatedPostID := range req.RelatedPostIDs {
		// Validate related post exists
		if err := s.validatePostExists(ctx, tenantID, relatedPostID); err != nil {
			// Log lỗi nhưng không dừng quá trình
			fmt.Printf("Warning: Related post %d not found: %v\n", relatedPostID, err)
			continue
		}

		// Kiểm tra không tạo mối quan hệ với chính nó
		if req.PostID == relatedPostID {
			continue
		}

		// Tạo mối quan hệ
		relatedPost := &models.RelatedPost{
			TenantID:        tenantID,
			PostID:          req.PostID,
			RelatedPostID:   relatedPostID,
			Priority:        1, // Mặc định priority là 1
			IsBidirectional: req.IsBidirectional,
			CreatedBy:       userID,
		}

		if err := s.relatedPostRepo.Create(ctx, relatedPost); err != nil {
			// Log lỗi nhưng không dừng quá trình
			fmt.Printf("Warning: Failed to create relation between %d and %d: %v\n", req.PostID, relatedPostID, err)
			continue
		}

		// Tạo mối quan hệ ngược lại nếu cần
		if req.IsBidirectional {
			// Kiểm tra xem mối quan hệ ngược đã tồn tại chưa
			reverseExists, err := s.relatedPostRepo.CheckRelationExists(ctx, tenantID, relatedPostID, req.PostID)
			if err != nil {
				// Log lỗi nhưng không dừng quá trình
				fmt.Printf("Warning: Failed to check reverse relation exists: %v\n", err)
				continue
			}

			if !reverseExists {
				reverseRelatedPost := &models.RelatedPost{
					TenantID:        tenantID,
					PostID:          relatedPostID,
					RelatedPostID:   req.PostID,
					Priority:        1, // Mặc định priority là 1
					IsBidirectional: req.IsBidirectional,
					CreatedBy:       userID,
				}

				if err := s.relatedPostRepo.Create(ctx, reverseRelatedPost); err != nil {
					// Log lỗi nhưng không dừng quá trình
					fmt.Printf("Warning: Failed to create reverse relationship: %v\n", err)
				}
			}
		}
	}

	// Trả về danh sách bài viết liên quan đã cập nhật
	return s.GetPostRelatedPosts(ctx, tenantID, websiteID, req.PostID, 100) // Giới hạn 100 bài viết liên quan
}

// DeletePostRelatedPost xóa một mối quan hệ bài viết liên quan cụ thể
func (s *relatedPostService) DeletePostRelatedPost(ctx context.Context, tenantID, websiteID uint, req request.DeletePostRelatedPostRequest) error {
	// Validate that post exists
	if err := s.validatePostExists(ctx, tenantID, websiteID, req.PostID); err != nil {
		return err
	}

	// Validate that related post exists
	if err := s.validatePostExists(ctx, tenantID, req.RelatedID); err != nil {
		return err
	}

	// Xóa mối quan hệ
	return s.relatedPostRepo.DeleteByPosts(ctx, tenantID, req.PostID, req.RelatedID)
}
