package handlers

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"

	"wnapi/internal/pkg/auth"
	"wnapi/internal/pkg/response"
	"wnapi/modules/blog/dto/request"
	"wnapi/modules/blog/service"
)

// AuthorHandler handles HTTP requests for blog authors
type AuthorHandler struct {
	authorService service.AuthorService
	jwtService    *auth.JWTService
}

// NewAuthorHandler creates a new author handler instance
func NewAuthorHandler(authorService service.AuthorService, jwtService *auth.JWTService) *AuthorHandler {
	return &AuthorHandler{
		authorService: authorService,
		jwtService:    jwtService,
	}
}

// getTenantAndWebsiteID gets both tenant and website IDs from context with validation
func getTenantAndWebsiteID(c *gin.Context) (tenantID, websiteID uint, ok bool) {
	tenantID = auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return 0, 0, false
	}

	websiteID = auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return 0, 0, false
	}

	return tenantID, websiteID, true
}

// Create handles the creation of a new author
func (h *AuthorHandler) Create(c *gin.Context) {
	// Get tenant and website IDs from context
	tenantID, websiteID, ok := getTenantAndWebsiteID(c)
	if !ok {
		return
	}

	// Parse request body
	var req request.CreateAuthorRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	author, err := h.authorService.CreateAuthor(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, author, nil)
}

// Get handles retrieval of an author by ID
func (h *AuthorHandler) Get(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get author ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Author ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing author ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	authorID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid author ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid author ID", "INVALID_ID", details)
		return
	}

	// Call service
	author, err := h.authorService.GetAuthor(c.Request.Context(), tenantID, websiteID, uint(authorID))
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, author, nil)
}

// GetByUserId handles retrieval of an author by user ID
func (h *AuthorHandler) GetByUserId(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get user ID from URL
	userIdStr := c.Param("user_id")
	if userIdStr == "" {
		details := []interface{}{map[string]string{"message": "User ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing user ID", "MISSING_USER_ID", details)
		return
	}

	// Convert to uint
	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid user ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid user ID", "INVALID_USER_ID", details)
		return
	}

	// Call service
	author, err := h.authorService.GetAuthorByUserId(c.Request.Context(), tenantID, websiteID, uint(userId))
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, author, nil)
}

// Update handles updating an existing author
func (h *AuthorHandler) Update(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get author ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Author ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing author ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	authorID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid author ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid author ID", "INVALID_ID", details)
		return
	}

	// Parse request body
	var req request.UpdateAuthorRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid request data", "INVALID_REQUEST", details)
		return
	}

	// Call service
	author, err := h.authorService.UpdateAuthor(c.Request.Context(), tenantID, websiteID, uint(authorID), req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, author, nil)
}

// Delete handles deletion of an author
func (h *AuthorHandler) Delete(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Get author ID from URL
	idStr := c.Param("id")
	if idStr == "" {
		details := []interface{}{map[string]string{"message": "Author ID is required"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Missing author ID", "MISSING_ID", details)
		return
	}

	// Convert to uint
	authorID, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		details := []interface{}{map[string]string{"message": "Invalid author ID format"}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid author ID", "INVALID_ID", details)
		return
	}

	// Call service
	err = h.authorService.DeleteAuthor(c.Request.Context(), tenantID, websiteID, uint(authorID))
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response
	response.Success(c, nil, nil)
}

// List handles listing of authors with pagination
func (h *AuthorHandler) List(c *gin.Context) {
	// Get tenant ID from context
	tenantID := auth.GetTenantID(c)
	if tenantID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin tenant", "TENANT_REQUIRED")
		return
	}

	// Get website ID from context
	websiteID := auth.GetWebsiteID(c)
	if websiteID == 0 {
		response.Error(c, http.StatusBadRequest, "Thiếu thông tin website", "WEBSITE_REQUIRED")
		return
	}

	// Parse query parameters
	var req request.ListAuthorRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		details := []interface{}{map[string]string{"message": err.Error()}}
		response.ErrorWithDetails(c, http.StatusBadRequest, "Invalid query parameters", "INVALID_QUERY", details)
		return
	}

	// Call service
	result, err := h.authorService.ListAuthors(c.Request.Context(), tenantID, websiteID, req)
	if err != nil {
		handleError(c, err)
		return
	}

	// Return success response with pagination metadata
	meta := &response.Meta{
		NextCursor: result.Meta.NextCursor,
		HasMore:    result.Meta.HasMore,
	}
	response.Success(c, result.Authors, meta)
}
